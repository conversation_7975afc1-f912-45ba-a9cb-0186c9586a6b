<?php

namespace app\modules\v1\components\FraudDetection;

use app\components\ErrorAlertLogger;
use app\models\objects\ErrorAlert;
use app\modules\v1\models\records\PaymentTypeRecord;
use GuzzleHttp\Exception\GuzzleException;
use Twilio\Exceptions\TwilioException;
use Yii;
use yii\db\Exception;
use yii\db\Expression;
use yii\helpers\Json;
use yii\base\InvalidConfigException;
use app\modules\v1\components\FraudDetection\Models\FraudScore;
use app\modules\v1\components\FraudDetection\Models\FraudSignal;
use app\modules\v1\components\FraudDetection\Models\FraudBlacklist;
use app\modules\v1\components\FraudDetection\Support\FraudListProvider;
use app\modules\v1\components\FraudDetection\Rules\AddressRule;
use app\modules\v1\components\FraudDetection\Rules\AddressGeoCodeRule;
use app\modules\v1\components\FraudDetection\Rules\FullNameRule;
use app\modules\v1\components\FraudDetection\Rules\PhoneNumberRule;
use app\modules\v1\components\FraudDetection\Rules\EmailAddressRule;
use app\modules\v1\components\FraudDetection\Rules\ArrayCompletenessRule;
use app\modules\v1\components\FraudDetection\Rules\RequestEnvironmentRule;

const GOOGLE_API_KEY = 'AIzaSyDmZVV7orP5-M3hq43pSa3MsdzNHUlR2NI';

class ScoringFraudDetector extends FraudDetector
{
    protected array $ruleMap = [];
    protected float $threshold = 7.0;
    protected FraudListProvider $listProvider;
    protected array $failures = [];

    /**
     * @param FraudListProvider|null $listProvider
     * @param float $threshold
     */
    public function __construct(?FraudListProvider $listProvider = null, float $threshold = 7.0)
    {
        $this->ruleMap = [
            'verifyFullName' => new FullNameRule(),
            'verifyEmailAddress' => new EmailAddressRule(),
            'verifyPhoneNumber' => new PhoneNumberRule(),
            'verifyAddress' => new AddressRule(),
            'verifyAddressGeoCode' => new AddressGeoCodeRule(),
            'verifyArrayCompleteness' => new ArrayCompletenessRule(),
            'verifyRequestEnvironment' => new RequestEnvironmentRule(),
        ];
        $this->threshold = $threshold;
        $this->listProvider = $listProvider ?: new FraudListProvider();

        foreach ($this->ruleMap as $rule) {
            if (method_exists($rule, 'setLogger')) {
                $rule->setLogger($this);
            }
        }
    }

    /**
     * @param array $checks
     * @return void
     */
    public function runChecks(array $checks): void
    {
        foreach ($checks as $check) {
            $method = $check['method'];
            $args = $check['args'] ?? [];

            if (isset($this->ruleMap[$method])) {
                $rule = $this->ruleMap[$method];
                $score = $rule->run(...$args);

                if ($score === null) {
                    continue;
                }

                $this->scores[$method] = $score;
                $this->totalScore += $score;
            }
        }
    }

    /**
     * @param array $checks
     * @param array $metadata
     * @param bool $persistOnFail
     * @return bool
     * @throws Exception
     */
    public function evaluate(array $checks, array $metadata = [], bool $persistOnFail = true): bool
    {
        $this->resetState();
        $this->runChecks($checks);

        $score = $this->getScore();
        $failures = $this->getFailures();
        $isValid = ($score >= $this->threshold);

        if (!$isValid && $persistOnFail) {
            $this->persistResult($score, $failures, $metadata);
        }

        return $isValid;
    }

    /**
     * @param array $baseChecks
     * @param array $expensiveChecks
     * @param array $metadata
     * @param float $margin
     * @return array
     * @throws Exception
     */
    public function evaluateWithEscalation(
        array $baseChecks,
        array $expensiveChecks,
        array $metadata = [],
        float $margin = 1.0
    ): array
    {
        $this->resetState();
        $this->runChecks($baseChecks);

        $baseScore = $this->getScore();
        $baseFailures = $this->getFailures();

        // Clear PASS: no escalation, no persistence needed.
        if ($baseScore >= $this->threshold) {
            if ($this->logAllScores) {
                $this->persistResult($baseScore, $baseFailures, $metadata);
            }

            return [
                'isValid' => true,
                'score' => $baseScore,
                'baseScore' => $baseScore,
                'extraScore' => 0.0,
                'ranExpensive' => false,
            ];
        }

        // Clear FAIL: even if we added 1 point, we still wouldn't hit a threshold?
        // No—here we use the provided $margin. If baseScore is *more than* margin
        // below a threshold, we fail now (no expensively spend).
        if ($baseScore < ($this->threshold - $margin)) {
            // Persist once with base data
            $this->persistResult($baseScore, $baseFailures, $metadata);
            return [
                'isValid' => false,
                'score' => $baseScore,
                'baseScore' => $baseScore,
                'extraScore' => 0.0,
                'ranExpensive' => false,
            ];
        }

        // --- Tier 2 (borderline → escalate) -------------------------------------
        // Borderline: append expensive checks on the *same* detector (no reset).
        // This accumulates scores/failures across both tiers.
        $this->runChecks($expensiveChecks);

        // After running expensive checks, the detector state now includes both tiers.
        $finalScore = $this->getScore();
        $combinedFailures = $this->getFailures();
        $extraScore = $finalScore - $baseScore;
        $finalValid = ($finalScore >= $this->threshold);

        // Persist *only if final fail*

        if ($this->logAllScores || !$finalValid) {
            $this->persistResult($finalScore, $combinedFailures, $metadata);
        }

        return [
            'isValid' => $finalValid,
            'score' => $finalScore,
            'baseScore' => $baseScore,
            'extraScore' => $extraScore,
            'ranExpensive' => true,
        ];
    }


    /**
     * @param string $ipAddress
     * @param array|null $paymentTypePks
     * @return bool|null
     * @throws InvalidConfigException
     */
    public function applyListOverrides(string $ipAddress, ?array $paymentTypePks = null): ?bool
    {
        if ($this->isWhitelisted($ipAddress, $paymentTypePks)) {
            return true;
        }

        if ($this->isBlacklisted($ipAddress)) {
            return false;
        }

        return null;
    }


    /**
     * @param int $failLimit
     * @param int $windowSeconds
     * @param array $paymentTypePks
     * @return bool
     */
    public function isVelocityExceededByPaymentTypePk(
        int   $failLimit,
        int   $windowSeconds,
        array $paymentTypePks,
    ): bool
    {
        $subquery = FraudScore::find()
            ->select(['paymentTypePk'])
            ->where(['in', 'paymentTypePk', $paymentTypePks])
            ->andWhere(['>=', 'dateCreated', new Expression("DATEADD(SECOND, -{$windowSeconds}, GETDATE())")])
            ->groupBy('paymentTypePk')
            ->having(new Expression("COUNT(*) + 1 >= {$failLimit}"))
            ->limit(1);

        return $subquery->exists();
    }


    /**
     * @param string $ip
     * @param int $failLimit
     * @param int $windowSeconds
     * @param array $paymentTypePks
     * @return bool
     * @throws InvalidConfigException
     */
    public function checkVelocityAndBlackListByPaymentTypePk(
        string $ip,
        int    $failLimit,
        int    $windowSeconds,
        array  $paymentTypePks,
    ): bool
    {
        $ipAddress = strtolower(trim($ip));
        if ($ipAddress === '') {
            return false;
        }

        $isVelocityExceeded = $this->isVelocityExceededByPaymentTypePk($failLimit, $windowSeconds, $paymentTypePks);

        if (!$isVelocityExceeded) {
            return false;
        }

        try {
            if (!$this->isBlacklisted($ipAddress)) {
                $bl = new FraudBlacklist();
                $bl->sourceType = 'ipAddress';
                $bl->sourceValue = $ipAddress;

                if ($this->fraudMode === self::FRAUD_MODE_ENFORCE) {
                    $bl->save(false);

                    // set disabledDateTime on PaymentType(s)
                    PaymentTypeRecord::disableIfVelocityExceeded($paymentTypePks, $windowSeconds, $failLimit);

                    $message = sprintf("PaymentType(s) disabled: %s. Velocity exceeded: %d in %d seconds.",
                        implode(',', $paymentTypePks), $failLimit, $windowSeconds);

                    $errorAlert = new ErrorAlert();
                    $errorAlert->setAttributes([
                        'title' => 'Notification',
                        'system' => ErrorAlertLogger::getSystem(),
                        'ipAddress' => $ipAddress,
                        'message' => $message,
                        'slackWebhookUrl' => ErrorAlertLogger::getSlackWebhookUrl()
                    ]);
                    $errorAlert->sendToPhone();
                    $errorAlert->sendToSlack();
                }
            }
        } catch (Exception|TwilioException|GuzzleException $e) {
            Yii::error($e->getMessage(), __METHOD__);
        }

        // Confirm final status
        return $this->isBlacklisted($ipAddress);
    }

    /**
     * @param string $ip
     * @param int $failLimit
     * @param int $windowSeconds
     * @param array|null $paymentTypePks
     * @param float|null $failBelow
     * @return bool
     */
    public function isVelocityExceededByScore(
        string $ip,
        int    $failLimit,
        int    $windowSeconds,
        ?array $paymentTypePks = null,
        ?float $failBelow = null
    ): bool
    {
        $failBelow ??= $this->threshold;
        $since = gmdate('Y-m-d H:i:s', time() - $windowSeconds);

        $q = FraudScore::find()
            ->where(['ipAddress' => $ip])
            ->andWhere(['<', 'score', $failBelow])
            ->andWhere(['>=', 'dateCreated', $since]);

        if (!empty($paymentTypePks)) {
            $q->andWhere(['paymentTypePk' => $paymentTypePks]);
        }

        return $q->count() >= $failLimit;
    }


    /**
     * @param string $ip
     * @param int $failLimit
     * @param int $windowSeconds
     * @param array|null $paymentTypePks
     * @param float|null $failBelow
     * @return bool
     * @throws InvalidConfigException
     */
    public function checkVelocityAndBlackListByScore(
        string $ip,
        int    $failLimit,
        int    $windowSeconds,
        ?array $paymentTypePks = null,
        ?float $failBelow = null
    ): bool
    {
        $ipAddress = strtolower(trim($ip));
        if ($ipAddress === '') {
            return false;
        }

        $failBelow = $failBelow ?? $this->threshold; // your detector's fail cutoff

        // Time window start
        $since = gmdate('Y-m-d H:i:s', time() - $windowSeconds);

        $q = FraudScore::find()
            ->where(['ipAddress' => $ipAddress])
            ->andWhere(['<', 'score', $failBelow])
            ->andWhere(['>=', 'dateCreated', $since]);

        // If $paymentTypePk is not null, add to filter
        if (!empty($paymentTypePks)) {
            $q->andWhere(['paymentTypePk' => $paymentTypePks]);
        }

        $failCount = (int)$q->count();

        if ($failCount < $failLimit) {
            return false;
        }

        try {
            if (!$this->isBlacklisted($ipAddress)) {
                $bl = new FraudBlacklist();
                $bl->sourceType = 'ipAddress';
                $bl->sourceValue = $ipAddress;

                if ($this->fraudMode === self::FRAUD_MODE_ENFORCE) {
                    $bl->save(false);
                }
            }
        } catch (Exception $e) {
            Yii::error($e->getMessage(), __METHOD__);
        }

        // Confirm final status
        return $this->isBlacklisted($ipAddress);
    }

    /**
     * @param string $method
     * @param string $rule
     * @param string $value
     * @return void
     */
    public function logFailure(string $method, string $rule, string $value): void
    {
        if (!isset($this->failures[$method])) {
            $this->failures[$method] = [];
        }

        $this->failures[$method][] = [
            'rule' => $rule,
            'value' => $value,
        ];
    }

    /**
     * @param string $method
     * @return array
     */
    public function getFailures(string $method = ''): array
    {
        if ($method !== '') {
            return $this->failures[$method] ?? [];
        }

        return $this->failures;
    }

    /**
     * @param string $ipAddress
     * @param array|null $paymentTypePks
     * @return bool
     */
    protected function isWhitelisted(string $ipAddress, ?array $paymentTypePks = null): bool
    {
        return $this->listProvider->isWhitelisted($ipAddress, $paymentTypePks);
    }

    /**
     * @param string $ipAddress
     * @return bool
     * @throws InvalidConfigException
     */
    protected function isBlacklisted(string $ipAddress): bool
    {
        return $this->listProvider->isBlacklisted($ipAddress);
    }

    /**
     * @param float $score
     * @param array $failuresByMethod
     * @param array $metadata
     * @return void
     * @throws Exception
     */
    protected function persistResult(
        float $score,
        array $failuresByMethod,
        array $metadata
    ): void
    {
        $ipAddress = trim((string)($metadata['ip'] ?? Yii::$app->request->userIP ?? ''));
        $userAgent = $metadata['userAgent'] ?? Yii::$app->request->userAgent;
        $paymentTypePks = $metadata['paymentTypePks'] ?? [];

        $ruleNamesByMethod = array_map(
            static fn(array $entries) => array_values(array_unique(array_column($entries, 'rule'))),
            $failuresByMethod
        );

        if (empty($paymentTypePks)) {
            $paymentTypePks = [null]; // Persist at least once; adjust to policy
        }

        foreach ($paymentTypePks as $paymentTypePk) {
            $fraudScore = new FraudScore([
                'ipAddress' => $ipAddress,
                'userAgent' => $userAgent,
                'paymentTypePk' => $paymentTypePk,
                'score' => $score,
                'rules' => Json::encode($ruleNamesByMethod, JSON_THROW_ON_ERROR),
            ]);

            if ($fraudScore->save()) {
                foreach ($failuresByMethod as $method => $entries) {
                    foreach ($entries as $entry) {
                        $signal = new FraudSignal([
                            'fraudScoreId' => $fraudScore->id,
                            'method' => $method,
                            'rule' => $entry['rule'],
                            'value' => $entry['value'] ?? null,
                        ]);
                        $signal->save();
                    }
                }
            } else {
                Yii::error("Failed to save FraudScore: " . Json::encode($fraudScore->errors), 'fraud');
            }
        }
    }

    /**
     * @return void
     */
    protected function resetState(): void
    {
        $this->scores = [];
        $this->failures = [];
        $this->totalScore = 0.0;
    }
}