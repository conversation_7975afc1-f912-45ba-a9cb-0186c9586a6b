<?php

namespace app\modules\v1\models\records;

use DateMalformedStringException;
use DateTime;
use Exception;
use Yii;
use yii\base\Exception as YiiBaseException;
use yii\base\InvalidConfigException;
use yii\db\ActiveQuery;
use yii\helpers\Json;

/**
 * Class MobileVisitorProfileRecord
 * @package app\modules\v1\models\records
 *
 * @property-read AddressRecord[] $addresses
 * @property-read BillRecord[] $bills
 * @property-read HistoryRecord[] $history
 * @property-read OrganizationRecord $organization
 * @property-read PaymentProfileRecord[] $paymentProfiles
 * @property-read ScheduledPaymentProfileRecord[] $scheduledPaymentProfiles
 */
class VisitorProfileRecord extends \app\models\records\VisitorProfileRecord
{
    public const string SCENARIO_CREATE = 'create';
    public const string SCENARIO_UPDATE = 'update';
    public const string SCENARIO_RESET_PASSWORD = 'resetPassword';
    public const string SCENARIO_AUTHENTICATE = 'authenticate';
    public const string SCENARIO_LAST_PASSWORD_CHANGE = 'lastPasswordChange';
    public const string SCENARIO_RESET_TOKEN = 'resetToken';
    public const string SCENARIO_FORGOT_PASSWORD = 'forgotPassword';
    public const string SCENARIO_VERIFY = 'verifyVisitor';

    /**
     * @return array
     */
    public function fields(): array
    {
        return ['id', 'cEmail', 'cToken', 'dLastPasswordChanged'];
    }

    /**
     * @return array|string[]
     */
    public function extraFields(): array
    {
        return [
            'paymentProfiles', 'addresses', 'scheduledPaymentProfiles'
        ];
    }

    /**
     * @return array|array[]
     */
    public function scenarios(): array
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = ['cEmail', 'cPassword'];
        $scenarios[self::SCENARIO_UPDATE] = ['cEmail'];
        $scenarios[self::SCENARIO_RESET_PASSWORD] = ['cEmail', 'cPassword', 'cToken'];
        $scenarios[self::SCENARIO_AUTHENTICATE] = ['cEmail', 'cPassword'];
        $scenarios[self::SCENARIO_LAST_PASSWORD_CHANGE] = ['cPassword', 'dLastPasswordChanged'];
        $scenarios[self::SCENARIO_FORGOT_PASSWORD] = ['cEmail', 'cPassword', 'cToken'];
        $scenarios[self::SCENARIO_VERIFY] = ['cEmail', 'cPassword', 'cToken'];

        return $scenarios;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['cEmail', 'cPassword'],
                'required',
                'on' => [self::SCENARIO_CREATE, self::SCENARIO_AUTHENTICATE]
            ],
            ['cToken',
                'required',
                'on' => self::SCENARIO_VERIFY
            ],
            [['cEmail'],
                'required',
                'on' => self::SCENARIO_UPDATE
            ],
            [['cEmail', 'cPassword', 'cToken'],
                'required',
                'on' => self::SCENARIO_RESET_PASSWORD
            ],
            ['cEmail',
                'unique',
                'on' => [self::SCENARIO_CREATE, self::SCENARIO_UPDATE],
                'message' => 'An account with this email address already exists'
            ],
            ['cEmail',
                'email',
                'checkDNS' => true
            ],
            [['iActive', 'iPasswordResetRequired', 'iMerchantManaged'],
                'integer'
            ],
            [['cSession'],
                'string'
            ],
            [['cLastLoggedIn'],
                'safe'
            ],
            [['iOrganizationFk'],
                'number'
            ],
            [['cEmail', 'cReturnURL'],
                'string',
                'max' => 250
            ],
            [['cPassword', 'cPasswordHint', 'cSecretAnswer', 'cPhone', 'cSecurityToken'],
                'string',
                'max' => 50
            ],
            [['cToken'],
                'string',
                'max' => 200
            ],
            [['dLastPasswordChanged'],
                'string',
                'max' => 100
            ],
            ### TODO: No UI exists yet for users to just change passwords, they have to do "Forgot password", disabling this rule for now
//            ['dLastPasswordChanged', 'validateLastPasswordChange', 'on' => self::SCENARIO_LAST_PASSWORD_CHANGE],
            [['iOrganizationFk'],
                'exist',
                'skipOnError' => true,
                'targetClass' => OrganizationRecord::class,
                'targetAttribute' => ['iOrganizationFk' => 'iOrganizationPk']
            ],
            [['passwordHistory'],
                'string',
                'max' => 500
            ],
        ];
    }

    /**
     * @return array|string[]
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'Visitor Pk',
            'cEmail' => 'Email',
            'cPassword' => 'Password',
            'cPasswordHint' => 'Password Hint',
            'cSecretAnswer' => 'Secret Answer',
            'cToken' => 'Token',
            'iActive' => 'Active',
            'cPhone' => 'Phone',
            'iPasswordResetRequired' => 'Password Reset Required',
            'cReturnURL' => 'Return Url',
            'cSession' => 'Session',
            'cLastLoggedIn' => 'Last Logged In',
            'dLastPasswordChanged' => 'Last Password Changed',
            'cSecurityToken' => 'Security Token',
            'iMerchantManaged' => 'Merchant Managed',
            'iOrganizationFk' => 'Organization Fk',
        ];
    }

    /**
     * @param bool $insert
     * @return bool
     * @throws YiiBaseException
     */
    public function beforeSave($insert): bool
    {
        if ($insert) {
            $this->cToken = Yii::$app->security->generateRandomString();
        }

        return parent::beforeSave($insert);
    }

    /**
     * Inline validator to enforce 90 day password policy
     *
     * @param string $attribute
     * @return void
     * @throws Exception
     */
    public function validateLastPasswordChange(string $attribute): void
    {
        try {
            $lastPasswordChanged = new DateTime($this->{$attribute});
        } catch (DateMalformedStringException $e) {
            // If the date is malformed, treat it as if password needs to be changed
            $this->addError($attribute, 'Invalid date format. Password update is required before you are able to login');
            return;
        }

        $dateToday = new DateTime();

        $interval = $lastPasswordChanged->diff($dateToday);

        if (!$interval->invert && $interval->days > 90) {
            $this->addError($attribute, 'Password update is required before you are able to login');
        }
    }

    /**
     * @return ActiveQuery
     * @throws InvalidConfigException
     */
    public function getAddresses(): ActiveQuery
    {
        // return $this->hasMany(AddressRecord::class, ['iMobileVisitorFk' => 'id']);
        return $this->hasMany(AddressRecord::class, ['iAddressPk' => 'iAddressFk'])
            ->viaTable('visitorProfileAddress', ['iMobileVisitorFk' => 'id'])
            ->indexBy('iAddressPk');
    }

    /**
     * @return ActiveQuery
     */
    public function getBills(): ActiveQuery
    {
        return $this->hasMany(BillRecord::class, ['iMobileVisitorFk' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getHistory(): ActiveQuery
    {
        return $this->hasMany(HistoryRecord::class, ['iMobileVisitorFk' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getOrganization(): ActiveQuery
    {
        return $this->hasOne(OrganizationRecord::class, ['iOrganizationPk' => 'iOrganizationFk']);
    }

    /**
     * @return ActiveQuery
     */
    public function getPaymentProfiles(): ActiveQuery
    {
        return $this->hasMany(PaymentProfileRecord::class, ['iMobileVisitorFk' => 'id']);
    }

    /**
     * @return ActiveQuery
     */
    public function getScheduledPaymentProfiles(): ActiveQuery
    {
        return $this->hasMany(ScheduledPaymentProfileRecord::class, ['iMobileVisitorFk' => 'id']);
    }

    /**
     * Checks to see if the password has already been used.
     *
     * @param string $encryptedPassword the attribute currently being validated
     * @return void
     */
    public function preventPasswordReuse(string $encryptedPassword): void
    {
        $passwordHistory = Json::decode($this->passwordHistory);

        if (!is_array($passwordHistory)) {
            return;
        }

        $previousPasswordFound = false;
        foreach ($passwordHistory as $previousPassword) {
            if ($encryptedPassword === $previousPassword) {
                $previousPasswordFound = true;
                break;
            }
        }

        if ($previousPasswordFound) {
            $this->addError('cPassword', 'This password has previously been used, choose a different password.');
        }
    }

    /**
     * @param string $password
     * @return void
     */
    public function updatePasswordHistory(string $password): void
    {
        $oldPasswords = Json::decode($this->passwordHistory);

        if (!is_array($oldPasswords)) {
            $oldPasswords = [];
        }

        # if there are 5 old passwords, remove the last one
        if (count($oldPasswords) === 5) {
            array_pop($oldPasswords);
        }

        # push the new password onto the beginning of the password history array
        array_unshift($oldPasswords, $password);
        $this->passwordHistory = Json::encode($oldPasswords);
    }
}
