<?php

namespace app\modules\v1\services;

use app\components\Constants;
use app\components\FtpService;
use app\components\Helper;
use app\modules\v1\models\objects\Ftp;
use app\modules\v1\models\objects\Response;
use app\modules\v1\models\records\BaseRecord;
use app\modules\v1\models\records\FileRecord;
use app\modules\v1\models\records\ImportFileRecord;
use app\modules\v1\models\records\PaymentTypeRecord;
use DateMalformedStringException;
use DateTime;
use Throwable;
use Yii;
use yii\base\InvalidConfigException;
use yii\db\StaleObjectException;
use yii\web\HeaderCollection;
use yii\web\UploadedFile;

class ImportService extends BaseService
{
    /**
     * @return string[]
     */
    public static function methods(): array
    {
        return ['POST'];
        # TODO: Add these in as they are integrated ['POST','PUT','DELETE']
    }

    /**
     * @param array $params
     * @return Response
     * @throws InvalidConfigException
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function run(array $params): Response
    {
        $headers = Yii::$app->request->getHeaders();
        $response = new Response;

        // Loop through each verification step
        foreach ([
                     fn() => $this->verifyOrganization($headers, $response),
                     fn() => $this->verifyPaymentTypesByOrganization($headers, $response, $params)
                 ] as $step) {
            $response = $step();
            if (!$response->success) {
                return $response;
            }
        }

        # Check for throttle limit
        $lastImportFile = ImportFileRecord::find()
            ->select(['dateStarted'])
            ->andWhere(['organizationId' => Yii::$app->user->identity->organizationId])
            ->orderBy(['id' => SORT_DESC])
            ->limit(1)
            ->asArray()
            ->one();

        if ($lastImportFile && !filter_var($headers->get('ignoreThrottle'), FILTER_VALIDATE_BOOLEAN)) {
            # reset response->success to false, because response->success is set to true from verifyOrganization at this point
            $response->success = false;
            $dateStarted = $lastImportFile['dateStarted'] ? strtotime($lastImportFile['dateStarted']) : time();
            $response = Helper::getThrottleLimit($dateStarted, $response);
        }

        if (!$response->success) {
            return $response;
        }

        switch ($this->requestMethod) {
            case 'POST':
                $response = $this->getFile($params);
                break;
        }

        return $response;
    }

    /**
     * @param array $params
     * @return Response
     * @throws StaleObjectException
     * @throws Throwable
     */
    public function getFile(array $params): Response
    {
        $response = new Response();

        if (!array_key_exists('ftp', $params)) {
            $response->errors['service'] = 'FTP parameters are required.';
            return $response;
        }

        $ftp = new Ftp();
        $ftp->setAttributes($params['ftp']);

        ### XPSV3-275, in case a 3rd party does not provide the 'emailErrorNotification' parameter
        if (!array_key_exists('emailErrorNotification', $params)) {
            $params['emailErrorNotification'] = Constants::DEV_EMAIL_ADDRESS;
        }

        # Store import into file import table to be processed by cron manager
        $importFile = new ImportFileRecord();
        ### TODO: If possible, use UploadedFile, like it is used in Admin frontend/controllers/ImportController and common/models/forms/ImportFileForm->saveFile()
        $importFile->setAttributes([
            'fileName' => $params['fileName'],
            'tableName' => $params['section'],
            'importImmediately' => $params['importNow'],
            'importDateTime' => $params['importNow'] ? null : $params['importDateTime'],
            'appendReplaceRecords' => $params['appendReplace'],
            'emailResultsTo' => $params['emailResults'] ? implode(',', $params['emailResults']) : '',
            'importStatus' => 'Pending',
            'organizationId' => $this->user->identity->organizationId,
        ]);

        if ($ftp->validate() && $importFile->validate()) {

            # Verify table/record exists
            $className = BaseRecord::NAMESPACE . '\\' . Helper::convertServiceName($params['section']) . 'Record';

            if (!class_exists($className)) {
                $response = new Response();
                $response->errors['service'] = sprintf('Invalid section name (%s)', $params['section']);
                return $response;
            }

            $module = Yii::$app->getModule('v1');
            if ($module === null) {
                $response->errors['service'] = 'Module is null';
                return $response;
            }

            $dirPath = $module->getBasePath() . DIRECTORY_SEPARATOR . $module->params['importDirectoryName'];
            $fileName = $importFile->fileName;

            $ftpService = new FtpService($ftp->attributes);

            try {
                $ftpService->connect();
            } catch (Throwable $e) {
                Yii::error(['ImportService->' . __FUNCTION__ . ', $ftpService->connect()' => Helper::generateErrorArray($e)]);
                $error = [
                    $e->getCode() => $e->getMessage(),
                ];

                $message = 'An error occurred when attempting to connect to the remote server.<br><br>';
                $this->sendErrorNotificationEmail([false, 'error' => $error], $message, $params, $fileName);

                $response->errors[] = $error;
                return $response;
            }

            $dirAndFileName = $dirPath . DIRECTORY_SEPARATOR . $fileName;
            $ftpResponse = $ftpService->download($fileName, $dirAndFileName);

            if (!$ftpResponse['success']) {
                $message = 'An error occurred when attempting to download a file from the remote server.<br><br>';
                $this->sendErrorNotificationEmail($ftpResponse, $message, $params, $fileName);

                $response->errors[] = $ftpResponse['error'];
                return $response;
            }

            if (!empty($importFile->fileName)) {
                $contentMimeType = mime_content_type($dirAndFileName);
                $importFile->scenario = in_array($contentMimeType, ['text/plain', 'application/csv']) ?
                    ImportFileRecord::SCENARIO_IGNORE_EXT_RULE : ImportFileRecord::SCENARIO_ENFORCE_EXT_RULE;

                # Check if the file mime zip is zip and if so extract the file and put it into UploadedFile object
                if ($contentMimeType === 'application/zip') {
                    $importFile->extractFile($dirAndFileName);
                    $fileName = $importFile->fileName;
                    $dirAndFileName = $dirPath . DIRECTORY_SEPARATOR . $fileName;

                    if ($importFile->hasErrors()) {
                        $response->errors[] = $importFile->errors;
                        return $response;
                    }
                }
            }

            # Rename file, so it doesn't get processed again
            ## Commented out, so it matches the file name in the import schedule
            ## currently renaming it doesn't allow for it to pull because file
            ## name doesn't match
//            $dotPos = strrpos($fileName, '.');
//            $namePart = substr($fileName, 0, $dotPos);
//            $datePart = time();//date('YmdHis');
//            $extPart = substr($fileName, $dotPos);
//            $newFileName = $namePart . '--' . $datePart . $extPart;
//            $ftp->rename($fileName, $newFileName);

            $file = new FileRecord();
            $file->name = $fileName;
            $file->size = filesize($dirAndFileName);
            $file->type = mime_content_type($dirAndFileName);
            $file->save(false);

            // upload to XPC Admin server
            $pathInfo = pathinfo($dirAndFileName);
            $fileExtension = is_array($pathInfo) && array_key_exists('extension', $pathInfo) ? strtolower($pathInfo['extension']) : 'txt';
            $uploadFileName = $file->guid . '.' . $fileExtension;

            $ftpServiceLocal = new FtpService($module->params['xpcFtp']);
            $ftpServiceLocal->directory = $module->params['xpcFtp']['path'];

            try {
                $ftpServiceLocal->connect();
            } catch (Throwable $e) {
                Yii::error(['ImportService->' . __FUNCTION__ . ', $ftpServiceLocal->connect()' => Helper::generateErrorArray($e)]);
                $error = [
                    $e->getCode() => $e->getMessage(),
                ];

                $message = 'An error occurred when attempting to connect to the local server.<br><br>';
                $this->sendErrorNotificationEmail([false, 'error' => $error], $message, $params, $fileName);

                $response->errors[] = $error;
                return $response;
            }

            $ftpResponse = $ftpServiceLocal->upload($ftpServiceLocal->directory . DIRECTORY_SEPARATOR . $uploadFileName, file_get_contents($dirAndFileName));

            # delete locally created file
            @unlink($dirAndFileName);

            if (!$ftpResponse['success']) {
                $message = 'An error occurred when attempting to save the file from the remote server.<br><br>';
                $this->sendErrorNotificationEmail($ftpResponse, $message, $params, $fileName);

                # delete the file record if it failed to upload to server
                $file->delete();

                $response->errors[] = $ftpResponse['error'];
                return $response;
            }

            # Store import into file import table to be processed by cron manager
            $importFile->fileId = $file->id;
            $importFile->save(false);

            foreach ($params['paymentTypes'] as $paymentType) {
                $billDate = null;
                if (isset($paymentType['billDate'])) {
                    try {
                        $billDate = (new DateTime($paymentType['billDate']))->format('m/d/Y');
                    } catch (DateMalformedStringException $e) {
                        // Log the error and use current date as fallback
                        \Yii::error("Invalid billDate format in import: '{$paymentType['billDate']}' - " . $e->getMessage(), __METHOD__);
                        $billDate = (new DateTime())->format('m/d/Y');
                    }
                }

                $paymentTypeRecord = PaymentTypeRecord::findOne($paymentType['id']);
                $importFile->link('importPaymentTypes', $paymentTypeRecord, [
                    'billDate' => $billDate,
                    'sendEmail' => empty($paymentType['sendEmail']) ? 0 : $paymentType['sendEmail']
                ]);
            }

            $response->success = true;
            $response->object = 'import';
            $response->data['import'] = 'Import was successfully created and queued to be processed.';

        } else {
            $response->errors = array_merge($ftp->errors, $importFile->errors);
        }

        return $response;
    }

    /**
     * @param array $ftpResponse
     * @param string $message
     * @param array $params
     * @param string $fileName
     * @return void
     */
    protected function sendErrorNotificationEmail(array $ftpResponse, string $message, array $params, string $fileName): void
    {
        ### Redact sensitive data
        $sensitiveData = ['password', 'sshKey', 'sshPassphrase'];
        foreach ($sensitiveData as $each) {
            if (array_key_exists($each, $params['ftp'])) {
                $params['ftp'][$each] = 'redacted';
            }
        }

        $errorCode = array_key_first($ftpResponse['error']);
        $errorMessage = $ftpResponse['error'][$errorCode];

        $message .= "Filename: $fileName<br><br>";
        $message .= "Error code: $errorCode<br><br>";
        $message .= "Error message: $errorMessage<br><br>";
        $message .= 'FTP info: ' . var_export($params['ftp'], true) . '<br><br>';

        $mailer = Yii::$app->mailer->compose()
            ->setFrom(Constants::NO_REPLY_EMAIL_ADDRESS)
            ->setTo(Helper::commaDelimitedStringToArray($params['emailErrorNotification']))
            ->setSubject('FAILURE: Import Exception Details')
            ->setHtmlBody($message);

        $mailer->send();
    }

    /**
     * @param HeaderCollection $headers
     * @param Response $response
     * @param array $params
     * @return Response
     * @throws InvalidConfigException
     */
    private function verifyPaymentTypesByOrganization(HeaderCollection $headers, Response $response, array $params): Response
    {
        $response->success = false; // reset it back to false, since the previous verifyOrganization could set it to true

        if (!empty($params['paymentTypes'])) {
            $organizationId = $headers['organizationid'];
            $paymentTypeIds = array_column($params['paymentTypes'], 'id');
            $paymentTypes = array_column(PaymentTypeRecord::find()
                ->select(['iPaymentTypePk'])
                ->andWhere(['iPaymentTypePk' => $paymentTypeIds])
                ->andWhere(['iOrganizationFk' => $organizationId])
                ->asArray()
                ->all(), 'iPaymentTypePk');
            $invalidPaymentTypeIds = array_diff($paymentTypeIds, $paymentTypes);

            if (!empty($invalidPaymentTypeIds)) {
                $response->errors['paymentTypes'] = 'The following payment type ids are not assigned to this organization.';
                $response->errors['paymentTypeIds'] = array_values($invalidPaymentTypeIds);
                return $response;
            }

            $response->success = true;
            return $response;
        }

        $response->errors['paymentTypes'] = 'At least one payment type is required to import bills.';
        return $response;
    }
}