<?php
/** @noinspection PhpClassConstantAccessedViaChildClassInspection */

namespace app\modules\v1\services;

use app\components\BinCheck;
use app\components\Constants;
use app\components\Formatter;
use app\components\gateways\BaseGateway;
use app\components\Helper as ComponentHelper;
use app\components\IINList;
use app\components\Twilio;
use app\components\validators\CardValidator;
use app\models\BaseQuery;
use app\models\exceptions\UnAuthorizedException;
use app\models\objects\ProcessorCheck;
use app\models\objects\ProcessorCreditCard;
use app\modules\v1\components\FraudDetection\ScoringFraudDetector;
use app\modules\v1\components\Helper;
use app\modules\v1\components\postbacks\BeaconPostBack;
use app\modules\v1\components\postbacks\GADoas;
use app\modules\v1\components\postbacks\GenericPostBack;
use app\modules\v1\components\postbacks\GenericPostBackV2;
use app\modules\v1\components\postbacks\GenericPostBackWithLast4;
use app\modules\v1\components\postbacks\IMTApps;
use app\modules\v1\components\postbacks\QuickBooks;
use app\modules\v1\components\postbacks\Retriever;
use app\modules\v1\components\postbacks\SpectrumPostBack;
use app\modules\v1\models\objects\Address;
use app\modules\v1\models\objects\BankAccount;
use app\modules\v1\models\objects\CreditCard;
use app\modules\v1\models\objects\Customer;
use app\modules\v1\models\objects\Pay;
use app\modules\v1\models\objects\Phone;
use app\modules\v1\models\objects\Response;
use app\modules\v1\models\objects\Transaction;
use app\modules\v1\models\objects\TransactionResponse;
use app\modules\v1\models\records\AddressRecord;
use app\modules\v1\models\records\BillRecord;
use app\modules\v1\models\records\BlackListRecord;
use app\modules\v1\models\records\EmailRecord;
use app\modules\v1\models\records\HistoryAddressRecord;
use app\modules\v1\models\records\HistoryLocatorRecord;
use app\modules\v1\models\records\HistoryRecord;
use app\modules\v1\models\records\PaymentProcessorRecord;
use app\modules\v1\models\records\PaymentProfileRecord;
use app\modules\v1\models\records\PaymentTypeLocatorRecord;
use app\modules\v1\models\records\PaymentTypeRecord;
use app\modules\v1\models\records\ScheduledPaymentProfileRecord;
use app\modules\v1\models\records\SiteFeeValidationRecord;
use app\modules\v1\models\records\TransactionDetailRecord;
use app\modules\v1\models\records\TransactionRecord;
use app\modules\v1\models\records\VisitorProfileRecord;
use Exception;
use kartik\validators\CardValidator as KartikCardValidator;
use Pachico\Magoo\Magoo;
use Pachico\Magoo\MagooArray;
use QuickBooksOnline\API\Exception\IdsException;
use QuickBooksOnline\API\Exception\SdkException;
use stdClass;
use Throwable;
use Yii;
use yii\base\ErrorException;
use yii\base\Exception as BaseException;
use yii\base\InvalidConfigException;
use yii\db\Exception as YiiDbException;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;
use yii\web\ServerErrorHttpException;

/**
 * Class PayService
 * @package app\modules\v1\services
 *
 * TODO: Add Blacklist to Pay Service
 * https://bitbucket.org/systemseastinc-dev/xpc-services/src/1a69602623ffcb9d27bae307d919b0e2ebafcb55/XPCCore/XPCv2.asmx.vb#lines-21312
 */
class PayService extends BaseService
{
    const int FRAUD_FAIL_LIMIT = 3;   // # failing scores to trigger auto-blacklist
    const int FRAUD_WINDOW_SEC = 240;  // velocity window in seconds
    const int PAYMENT_TYPE_REENABLE_AFTER_SECONDS = 20 * 60;   // # seconds to disable a payment type, set to 20 minutes

    /**
     * @return string[]
     */
    public static function methods(): array
    {
        return ['POST'];
    }

    /**
     * @param array $params
     * @return Response
     * @throws ErrorException
     * @throws InvalidConfigException
     * @throws YiiDbException
     */
    public function run(array $params): Response
    {
        $headers = Yii::$app->request->getHeaders();

        $response = new Response;

        switch ($this->requestMethod) {
            case 'POST':
                switch ($headers['method']) {
                    case 'auth':
                        $response = $this->auth($params);
                        break;
//                    case 'void':
//                        $response = $this->void($params);
//                        break;
                    case 'pay':
                    default:
                        $response = $this->pay($params);
                        break;
                }

//            case 'PUT':
//                $response = $this->update($params);
//                break;
//            case 'DELETE':
//                $response = $this->delete();
//                break;
//            default: # Any other request method
//                $response = new Response;
//                $response->errors['requestMethod'] = 'Invalid request method. Only POST is allowed.';
//                break;
        }

        # TODO: remove after testing memory limit errors
//        Yii::info(['memUsage' => [
//            'marker' => 'payService->run() before return $response',
//            'getElapsedTime()' => Yii::getLogger()->getElapsedTime(),
//            'usage' => Helper::getMemoryUsage(),
//        ]]);

        return $response;
    }


    /**
     * @param array $params
     * @return Response
     * @throws Exception
     * @throws ErrorException
     * @throws InvalidConfigException
     * @throws YiiDbException
     */
    public function pay(array $params): Response
    {
        ComponentHelper::logRequest(__METHOD__, $params);

        # Check if paymentProfilePk is passed in
        if (!empty($params['paymentProfileId'])) {
            $headers = Yii::$app->request->getHeaders();
            $response = $this->verifyVisitor($headers, (new Response()));

            if (!$response->success) {
                return $response;
            }

            $params = PaymentProfileRecord::loadPaymentProfileDetails($params);

            if (empty($params)) {
                $response->success = false;
                $response->errors['paymentProfileId'] = 'The payment profile you attempted to use no longer exists.';
                return $response;
            }
        }

        /*
         * Fraud detection is only required for instant payment types
         * If more than one pk, then if any of the pk's are not instant
         * or IVR, then fraud detection is not required
         *
         * If isSmartCardDevice, then fraud detection is not required
         */
        $response = new Response();

        # Check if payment is a Smart Card Device
        $isSmartCardDevice = !empty($params['isSmartCardDevice']);
        $isPaymentProfile = !empty($params['paymentProfileId']);

        $paymentMethod = trim($params['paymentMethod'] ?? Pay::PAYMENT_METHOD_CARD);
        $paymentTypePks = array_column($params['bills'], 'paymentTypePk');

        $existsQuery = PaymentTypeRecord::find()
            ->alias('t1')
            ->joinWith(['merchantInfo t2'], true, 'JOIN')
            ->andWhere(['t1.iPaymentTypePk' => array_unique($paymentTypePks)]);

        if ($paymentMethod === Pay::PAYMENT_METHOD_CARD) {
            $dynamicOr = ['t2.iCCPaymentProcessorFk' => [36, 46]];
        } else {
            $dynamicOr = ['t2.iCheckPaymentProcessorFk' => [36, 46]];
        }

        $existsQuery->andWhere([
            'or',
            ['<>', 't1.Type', 'Instant'],
            ['t1.btc' => 2],
            ['t1.activityType' => 3],
            $dynamicOr
        ]);

        $requiresFraudDetection = !($existsQuery->exists() || $isSmartCardDevice || $isPaymentProfile);

        if ($requiresFraudDetection) {
            $response = $this->runFraudDetector($params, $response);

            if (!$response->success) {
                return $response;
            }
        }

        $response = new Response();

        # This should never be true, unless they modify the amount
        # prior to sending to the pay service after validating in
        # the site fee service
        $siteFeeService = new SiteFeeService;
        $params['includeBillSiteFees'] = true;
        $siteFeeResponse = $siteFeeService->run($params);

        if (!$siteFeeResponse['success']) {
            return $siteFeeResponse;
        }

        $paymentTypeRecords = PaymentTypeRecord::find()
            ->joinWith([
                'merchantInfo.checkProcessor.processorFields' => static function (BaseQuery $query) {
                    $query->alias('checkProcessorFields');
                },
                'merchantInfo.creditCardProcessor.processorFields' => static function (BaseQuery $query) {
                    $query->alias('cardProcessorFields');
                },
            ])
            ->andWhere(['iPaymentTypePk' => array_unique(array_column($params['bills'], 'paymentTypePk'))])
            ->all();

        $isIVR = (bool)array_filter(
            array_column($paymentTypeRecords, 'btc'),
            static fn($item) => $item === 2
        );
        $isOutGoingPayment = (bool)array_filter(
            array_column($paymentTypeRecords, 'activityType'),
            static fn($item) => $item === 3
        );

        $isAddressRequired = (bool)array_filter(
            array_column($paymentTypeRecords, 'iAddressRequired'),
            static fn($item) => $item === 1 && !$isIVR && !$isSmartCardDevice && !$isOutGoingPayment
        );

        $isPostalCodeRequired = (bool)array_filter(
            array_column($paymentTypeRecords, 'iZipRequired'),
            static fn($item) => $item === 1 && !$isSmartCardDevice && !$isOutGoingPayment
        );

        $isEmailRequired = (bool)array_filter(
            array_column($paymentTypeRecords, 'iEmailRequired'),
            static fn($item) => $item === 1 && !$isIVR && !$isSmartCardDevice && !$isOutGoingPayment
        );

        $isPhoneRequired = (bool)array_filter(
            array_column($paymentTypeRecords, 'iPhoneRequired'),
            static fn($item) => $item === 1 && empty($params['paymentProfileId']) && !$isSmartCardDevice && !$isOutGoingPayment
        );

        $isCVVRequired = (bool)array_filter(
            array_column($paymentTypeRecords, 'iCVVRequired'),
            static fn($item) => $item === 1 && empty($params['scheduledPaymentProfileId']) && !$isSmartCardDevice
        );

        ### Do not require full name on IVR payments
        $isFullNameRequired = !$isIVR && !$isSmartCardDevice;

        ### TODO: Bring back shipping address eventually
//        $isShippingAddressRequired = array_filter(
//            array_column($paymentTypeRecords, 'iShippingAddressRequired'),
//            static fn($item) => $item === 1
//        );

        [$params, $customerParams, $bankAccountParams, $creditCardParams] = $this->splitParams($params);
//        if ($isSmartCardDevice) {
//            Yii::info(['$isSmartCardDevice' => [
//                '$params' => $params,
//                '$customerParams' => $customerParams,
//                '$creditCardParams' => $creditCardParams,
//            ]], __METHOD__);
//        }

        $pay = new Pay();
        $pay->setAttributes($params);
        $isValid = true;

        $customer = new Customer();
        $pay->customer = $customer;

        if (!empty($customerParams) && !$isSmartCardDevice) {
            $customer->setAttributes($customerParams);

            if ($isFullNameRequired) {
                $customer->scenario = Customer::SCENARIO_FULL_NAME_REQUIRED;

                if (!$customer->validate()) {
                    $response->errors['customer'] = $customer->errors;
                }
            }

            # Check if the customer address exists
            if (is_a($customer->address, Address::class)) {
                Helper::lookupCityState($customer->address, $response);

                if ($isAddressRequired && $isPostalCodeRequired) {
                    $customer->address->scenario = Address::SCENARIO_REQUIRED;
                } else if ($isAddressRequired) {
                    $customer->address->scenario = Address::SCENARIO_ADDRESS_REQUIRED;
                } else if ($isPostalCodeRequired) {
                    $customer->address->scenario = Address::SCENARIO_POSTAL_CODE_REQUIRED;
                }

                $isValid = $customer->address->validate();
                if ($customer->address->hasErrors()) {
                    $response->errors['address'] = $customer->address->errors;
                }
            }

            # Check if customer phone exists
            if (is_a($customer->phone, Phone::class)) {
                if ($isPhoneRequired) {
                    $customer->phone->scenario = Phone::SCENARIO_PHONE_REQUIRED;
                }

                $isValid = $isValid && $customer->phone->validate();
                if ($customer->phone->hasErrors()) {
                    $response->errors['phone'] = $customer->phone->errors;
                }
            }

            if ($isEmailRequired) {
                $customer->scenario = Customer::SCENARIO_EMAIL_REQUIRED;
            }

            ### $customer is validated twice
            if (!$customer->validate(null, false)) {
                $isValid = false;
                $response->errors['customer'] = isset($response->errors['customer']) ? array_merge($customer->errors, $response->errors['customer']) : $customer->errors;
            }
        }

        $bankAccount = new BankAccount();
        $creditCard = new CreditCard();

        # Check payment method
        if ($pay->paymentMethod === Pay::PAYMENT_METHOD_CHECK) {
            # check payment type to verify check payment is allowed.
            $isPaymentMethodAllowed = $this->isCheckPaymentMethodAllowed($paymentTypeRecords);

            if (!$isPaymentMethodAllowed) {
                $response->errors['paymentMethod'] = 'Payment method can only be "' . Pay::PAYMENT_METHOD_CARD . '".';
                return $response;
            }

            # Check if site fees calculated matches site fees being passed in
            ### Round the variables before comparison to deal with rounding issue
            ### {"success":true,"object":"siteFees","data":{"cardSiteFee":51.***************,"checkSiteFee":11.8}}
            if (round($siteFeeResponse['data']['checkSiteFee'], 2) !== round($params['siteFees'], 2)) {
                $response->errors['siteFees'] = 'Site fee amount is incorrect based on bill amount(s)';
                return $response;
            }

            if (!empty($bankAccountParams)) {
                $bankAccount->setAttributes($bankAccountParams);
                $pay->bankAccount = $bankAccount;
            }

            $isValid = $isValid && $bankAccount->validate();
            if ($bankAccount->hasErrors()) {
                $response->errors['bankAccount'] = $bankAccount->errors;
            }
        } else if ($pay->paymentMethod === Pay::PAYMENT_METHOD_CARD) {
            # check payment type to verify check payment is allowed.
            $isPaymentMethodAllowed = $this->isCardPaymentMethodAllowed($paymentTypeRecords);

            if (!$isPaymentMethodAllowed) {
                $response->errors['paymentMethod'] = 'Payment method can only be "' . Pay::PAYMENT_METHOD_CHECK . '".';
                return $response;
            }

            # Check if site fees calculated matches site fees being passed in
            ### Round the variables before comparison to deal with rounding issue
            ### {"success":true,"object":"siteFees","data":{"cardSiteFee":51.***************,"checkSiteFee":11.8}}
            if (round($siteFeeResponse['data']['cardSiteFee'], 2) !== round($params['siteFees'], 2)) {
                $response->errors['siteFees'] = 'Site fee amount is incorrect based on bill amount(s)';
                return $response;
            }

            if (!empty($creditCardParams)) {
                $creditCard->setAttributes($creditCardParams);
                $creditCard->isSmartCardDevice = $isSmartCardDevice;

                ### Recalculate the cardBrand because it can be provided in an inconsistent manner
                if (!empty($creditCard->cardNumber)) {
                    ### XPSV3-283: Remove non-numeric values from cardNumber
                    $creditCard->cardNumber = preg_replace('/\D/', '', $creditCard->cardNumber);
                    $creditCard->cardBrand = CardValidator::getCardBrand($creditCard->cardNumber);
                }
                $pay->creditCard = $creditCard;
            }

            # verify that card type is allowed across all payment types
            #TODO: Let Jim and Peter know that there is no validation against payment type, card types allowed with Magtek,
            #TODO: Magtek doesn't tell us what card brand is until after the transaction is completed.
            if (!$isSmartCardDevice) {
                $paymentRecordsArray = Json::decode(Json::encode($paymentTypeRecords));

                if ($isCVVRequired) {
                    $creditCard->scenario = CreditCard::SCENARIO_CVV_REQUIRED;
                }

                $validLuhn = KartikCardValidator::luhnCheck($creditCard->cardNumber);
                if ($validLuhn) {
                    $this->calcCardBrandAllowed($creditCard, $paymentRecordsArray);
                } else {
                    $creditCard->addError('cardNumber', "\"$creditCard->cardNumber\" is not a valid card number");
                }

                $isDebitCardOnly = (bool)array_filter(
                    array_column($paymentTypeRecords, 'debitCardOnly'),
                    static fn($item) => $item === 1 && !empty($creditCardParams)
                );

                if ($isDebitCardOnly && $validLuhn) {
                    $this->binLookupCombined($creditCard);
                }
            }

            $isValid = $isValid && $creditCard->validate(null, false);
            if ($creditCard->hasErrors()) {
                $response->errors['creditCard'] = $creditCard->errors;
            }
        } else {
            $response->errors['paymentMethod'] = 'Payment methods can only be "' . Pay::PAYMENT_METHOD_CARD . '" or "' . Pay::PAYMENT_METHOD_CHECK . '".';
            return $response;
        }

        if (!$pay->validate()) {
            $isValid = false;
            $response->errors = array_merge($pay->errors, $response->errors);
        }

        if ($isValid) {
            $transaction = new Transaction();
            $transaction->setAttributes($params);
            $transaction->customer = $customer;

            if ($pay->paymentMethod === Pay::PAYMENT_METHOD_CHECK) {
                $transaction->paymentMethod = Pay::PAYMENT_METHOD_CHECK;
                $transaction->bankAccount = $bankAccount;
                $cardBrandName = null;
                $bankAccountType = $transaction->bankAccount->bankAccountType ?? null;
            } else { // $pay->paymentMethod === Pay::PAYMENT_METHOD_CARD
                $transaction->paymentMethod = Pay::PAYMENT_METHOD_CARD;
                $transaction->creditCard = $creditCard;
                $cardBrandName = $creditCard->cardBrand;
                $bankAccountType = null;
            }

            # Apply site fee to bill for storing in history table
            $transactionAmounts = [];
            $billInterest = 0;
            foreach ($params['bills'] as $billIndex => $bill) {
                $paymentTypeIndex = array_search((int)$bill['paymentTypePk'], array_column($paymentTypeRecords, 'iPaymentTypePk'), true);
                /** @var PaymentTypeRecord $paymentTypeRecord */
                $paymentTypeRecord = $paymentTypeRecords[$paymentTypeIndex];

                # check if bill record exists
                if ($paymentTypeRecord->isTypePosted()) {
                    ### TODO: Probably doesn't need the joinWith statement
                    $billRecord = BillRecord::find()
                        ->joinWith(['paymentType.coBrandStripeImage', 'paymentTypeLocators'])
                        ->andWhere(['iBillPk' => $bill['billPk']])
                        ->andWhere([BillRecord::tableName() . '.iPaymentTypeFk' => $bill['paymentTypePk']])
                        ->limit(1)
                        ->asArray()
                        ->one();

                    if (empty($billRecord)) {
                        $response->errors['billPk' . $bill['billPk']] = 'Bill (pk: ' . $bill['billPk'] . ') not found';
                        break;
                    }

                    #check if dependency bills are in the array of bills being paid
                    $dependentBills = BillRecord::getDependentBills($params['bills'], 'paymentTypePk');
                    $dependentBills = array_column($dependentBills, 'dependentBill');

                    if ($dependentBills) {
                        $response->errors[] = 'Bills being paid have other bill dependencies to be paid first.';
                        break;
                    }

                    # Check if interest is not empty, if so validate the interest amount is correct
                    # check calculated interest amount
                    $calcInterestAmount = Helper::calculateInterest(
                        $billRecord['BillDate'],
                        $billRecord['paymentType']['iPaymentTypePk'],
                        $bill['billAmount']
                    );

                    $billInterest = $calcInterestAmount + $billRecord['Interest'];

                    if (isset($bill['interestAmount']) && (float)$bill['interestAmount'] > 0 && (float)$billInterest !== (float)$bill['interestAmount']) {
                        $response->errors['billPk' . $bill['billPk']] = 'Interest amount does not match correct interest of $' . $billInterest;
                        break;
                    }
                }

                if ($paymentTypeRecord->isTypePosted()) {
                    $totalBillAmount = $bill['billAmount'] + ($billInterest ?? 0);
                } else {
                    ### $bill['interestAmount'], really means an extra amount, interest/late fee/gratuity
                    $totalBillAmount = $bill['billAmount'] + ($bill['interestAmount'] ?? 0);
                }

                if ($pay->paymentMethod === Pay::PAYMENT_METHOD_CARD && isset($siteFeeResponse->data['billSiteFees']['cardSiteFee'][$billIndex])) {
                    $bill['siteFees'] = $siteFeeResponse->data['billSiteFees']['cardSiteFee'][$billIndex];
                    $siteFeeAmount = $bill['siteFees']['siteFee'];
                } else if ($pay->paymentMethod === Pay::PAYMENT_METHOD_CHECK && isset($siteFeeResponse->data['billSiteFees']['checkSiteFee'][$billIndex])) {
                    $bill['siteFees'] = $siteFeeResponse->data['billSiteFees']['checkSiteFee'][$billIndex];
                    $siteFeeAmount = $bill['siteFees']['siteFee'];
                } else {
                    throw new HttpException(500, 'Site fee not set at bill level, PayService.');
                }

                $merchantInfoRecord = $paymentTypeRecord->merchantInfo;

                if (empty($transactionAmounts[$paymentTypeRecord->iOrganizationFk])) {
                    $transactionAmounts[$paymentTypeRecord->iOrganizationFk] = [];
                }

                # combine amounts of all payment types with the same gateway id and credentials
                $dualHash = null;
                $primaryForm = null;
                $secondaryForm = null;
                [$primaryForm, $secondaryForm, $merchantInfoData] = $this->createGatewayObjects($pay->paymentMethod, $paymentTypeRecord);

                $processorClass = $pay->paymentMethod === 'Check' ? ProcessorCheck::class : ProcessorCreditCard::class;
                $mainHash = $merchantInfoRecord->calculateHash($processorClass, $primaryForm);

                ### iSplitCharge = 2 === DUAL DEPOSIT
                if ($paymentTypeRecord->organization->iSplitCharge === 2 && !$paymentTypeRecord->iZeroSiteFee && $siteFeeAmount > 0) {
                    $dualHash = $merchantInfoRecord->calculateHash($processorClass, $secondaryForm);
                }

                # Primary
                if (empty($transactionAmounts[$paymentTypeRecord->iOrganizationFk][$mainHash])) {
                    $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$mainHash] = array_merge(
                        [
                            'type' => 'primary',
                            'gateway' => array_merge($merchantInfoData, ($primaryForm[$pay->paymentMethod === 'Check' ? 'eCheck' : 'creditCard'] ?? []))
                        ],
                        [
                            'bills' => [],
                            'transactionAmount' => 0,
                        ]
                    );
                }

                $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$mainHash]['transactionAmount'] += $totalBillAmount;

                # Dual Deposit
                if ($dualHash) {
                    if (empty($transactionAmounts[$paymentTypeRecord->iOrganizationFk][$dualHash])) {
                        $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$dualHash] = array_merge(
                            [
                                'type' => 'secondary',
                                'linkedHash' => $mainHash,
                                'gateway' => array_merge($merchantInfoData, ($secondaryForm[$pay->paymentMethod === 'Check' ? 'eCheck' : 'creditCard'] ?? []))
                            ],
                            [
//                                'bills' => [],
                                'transactionAmount' => 0,
                            ]
                        );
                    }

                    $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$dualHash]['transactionAmount'] += $siteFeeAmount;
                } else {
                    $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$mainHash]['transactionAmount'] += $siteFeeAmount;
                }

                if (isset($bill['billPk']) && $paymentTypeRecord->isTypePosted()) {
                    ### TODO: Probably doesn't need the joinWith statement
                    $billRecord = BillRecord::find()
                        ->joinWith(['paymentType.coBrandStripeImage', 'paymentType.backgroundImage', 'paymentTypeLocators', 'paymentType.organization'])
                        ->andWhere([BillRecord::tableName() . '.iBillPk' => $bill['billPk']])
                        ->limit(1)
                        ->asArray()
                        ->one();

                    if ($billRecord) {
                        $mappedLocators = $this->createMappedLocators($billRecord['paymentTypeLocators']);

                        $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$mainHash]['bills'][] = [
                            'bill' => array_merge($billRecord, [
                                'organizationPk' => $paymentTypeRecord->iOrganizationFk,
                                'organization' => $paymentTypeRecord->organization->attributes,
                                'paymentTypePk' => $paymentTypeRecord->iPaymentTypePk,
                                'paymentType' => $paymentTypeRecord->attributes,
                                'billAmount' => $bill['billAmount'],
                                'paymentTypeLocators' => $mappedLocators,
                                'interestAmount' => $billInterest,
                                'siteFees' => $bill['siteFees']
                            ]),
                            'siteFee' => $siteFeeAmount
                        ];
                    }
                } else {
                    $bill['organizationPk'] = $paymentTypeRecord->iOrganizationFk;
                    $bill['organization'] = $paymentTypeRecord->organization->attributes;
                    $bill['paymentType'] = $paymentTypeRecord->attributes;
                    $transactionAmounts[$paymentTypeRecord->iOrganizationFk][$mainHash]['bills'][] = [
                        'bill' => $bill,
                        'siteFee' => $siteFeeAmount
                    ];
                }

                if ($billIndex % 50 === 0) {
                    gc_collect_cycles();
                }
            }

            if (empty($response->errors)) {
                $previousTransactions = [];
                $errorTransactionResponse = null;
                $isDuplicateTransaction = false;
                foreach ($transactionAmounts as $processors) {
                    foreach ($processors as $transactionHash => $processor) {
                        $uniqueBillIdentifiers = [];
                        $billLocators = [];
                        try {
                            if (array_key_exists('bills', $processor) && is_array($processor['bills'])) {
                                foreach ($processor['bills'] as $each) {
                                    if ($uid = Helper::calcUniqueId($each['bill'])) {
                                        $uniqueBillIdentifiers[] = $uid;
                                    }
                                }

                                array_walk($processor['bills'], static function ($each) use (&$billLocators) {
                                    if (!empty($each['bill']['paymentTypeLocators'])) {
                                        $billLocators[] = $each['bill']['paymentTypeLocators'];
                                    }
                                });
                            }
                        } catch (Exception|Throwable $e) {
                            Yii::error([
                                'calcUniqueId' => [
                                    '$uniqueBillIdentifiers' => $uniqueBillIdentifiers,
                                    '$processor' => $processor,
                                    '$e::class' => $e::class,
                                    '$e->getLine()' => $e->getLine(),
                                    '$e->getCode()' => $e->getCode(),
                                    '$e->getMessage()' => $e->getMessage(),
                                    '$e->getTrace()' => $e->getTrace(),
                                ]
                            ]);
                        }

                        $authTransaction = new Transaction($transaction->attributes);
                        ### XPSV3-111, set uniqueBillIdentifiers to store data to send to the gateways
                        $authTransaction->uniqueBillIdentifiers = $uniqueBillIdentifiers;
                        $authTransaction->billLocators = $billLocators;

                        ### Scenario, payment type only supports card, but user paid via check
                        $gatewayCredentials = $processor['gateway'];
                        $processorRecord = PaymentProcessorRecord::find()
                            ->andWhere(['iPaymentProcessorPk' => $gatewayCredentials['processorPk']])
                            ->limit(1)
                            ->one();

                        ### XPSV3-323: Throw an error if the $processorRecord is not configured in the admin
                        if ($processorRecord === null) {
                            throw new ServerErrorHttpException('An error has occurred, please contact support');
                        }

                        /** @var BaseGateway $gateway */
                        $gateway = new $processorRecord->className($gatewayCredentials);

                        ### Switch the gateway endpoint if the environment is not PROD
                        if ((YII_ENV_DEV || YII_ENV_SANDBOX) && $gatewayCredentials['gatewayEnvironment'] === 'live') {
                            $gateway->environment = BaseGateway::PRODUCTION;
                        }

                        if (isset($params['customFields'])) {
                            $authTransaction->customFields = array_merge($authTransaction->customFields, $params['customFields']);
                            $gateway->customFields = array_merge($gateway->customFields, $params['customFields']);
                        }

                        if (!($processor['transactionAmount'] > 0)) {
                            $response->errors['billAmount'] = 'Amount must be greater than 0.';
                            return $response;
                        }

                        ### XPSV3-114: Round the float value to 2 decimal places. Authorize.net throws errors otherwise
                        $authTransaction->amount = round($processor['transactionAmount'], 2);
                        $authTransaction->gatewayCredentials = $gatewayCredentials;

                        // Check for duplicate transaction, based on specific criteria in the payment data
                        if (empty($params['scheduledPaymentProfileId']) && $prevTransaction = $gateway->checkPreviousTransaction($authTransaction)) {
                            $prevResponse = $gateway->prevResponse($prevTransaction);
                            $isDuplicateTransaction = true;
                            break 2;
                        }

                        /** @var TransactionResponse $transactionResponse */
                        if ($pay->paymentMethod === Pay::PAYMENT_METHOD_CHECK) {
                            $transactionResponse = $gateway->chargeCheck($authTransaction);
                        } else {
                            $transactionResponse = $gateway->authorizeCreditCard($authTransaction);

                            if ($gateway::class === 'app\components\gateways\MagtekTSYS') {
                                ### Debugging code to figure out when some data is set in customFields
                                //Yii::info([
                                //    'authorizeCreditCard - $transactionResponse' => Json::decode(Json::encode($transactionResponse))
                                //]);

                                ### Get the "Card Brand" for Magtek processor
                                $customFields = Json::decode(Json::encode($transactionResponse))['customFields'] ?? null;
                                if ($cardBrandName === null && !empty($customFields['cardBrand'])) {
                                    $cardBrandName = $customFields['cardBrand'];
                                }
                            }
                        }

                        if ($transactionResponse->isError) {
                            $errorTransactionResponse = $transactionResponse;
                            $previousTransactions[] = [
                                'gateway' => $gateway,
                                'type' => $processor['type'],
                                'transaction' => $authTransaction,
                                'transactionResponse' => $transactionResponse,
                                'bills' => $processor['bills'] ?? [],
                                'transactionHash' => $transactionHash,
                                'processorRecord' => $processorRecord,
                                'processor' => $processor,
                            ];
                            break 2;
                        }

                        $previousTransactions[] = [
                            'gateway' => $gateway,
                            'type' => $processor['type'],
                            'transaction' => $authTransaction,
                            'transactionResponse' => $transactionResponse,
                            'bills' => $processor['bills'] ?? [],
                            'transactionHash' => $transactionHash,
                            'processorRecord' => $processorRecord,
                            'processor' => $processor,
                        ];
                    }
                }

                # Check if it is a duplicate,
                # if so return the original transactions response
                if ($isDuplicateTransaction) {
                    if ($prevResponse->isError) {
                        $response->errors[$prevResponse->responseCode] = $prevResponse->responseMessage;
                        return $response;
                    }

                    $response->success = true;
                    $response->object = 'transaction';
                    $response->data['transaction'] = 'Transaction completed';

                    if ($pay->includeHistoryIds) {
                        $arrHistory = [];
                        foreach ($prevTransaction['history'] as $historyRecord) {
                            $arrHistory[] = ['historyId' => $historyRecord['id'], 'transactionIds' => [$prevTransaction['id']]];
                        }

                        $response->data['historyRecords'] = $arrHistory;
                    }

                    $response->data['transactionId'] = $prevTransaction['transactionId'];

                    return $response;
                }

                if ($errorTransactionResponse) {
                    $response->errors[$errorTransactionResponse->responseCode] = $errorTransactionResponse->responseMessage;

                    foreach ($previousTransactions as $previousTransactionKey => $previousTransaction) {
                        $failedTransaction = new TransactionRecord();
                        $failedTransaction->paymentProcessorId = $previousTransaction['processorRecord']->iPaymentProcessorPk;
                        $failedTransaction->transactionId = $errorTransactionResponse->transactionId;
                        $failedTransaction->transactionStatus = $errorTransactionResponse->isError ? TransactionRecord::STATUS_FAILED : TransactionRecord::STATUS_SUCCESS;

                        if ($previousTransaction['type'] === 'secondary' && !empty($processor['linkedHash'])) {
                            $linkedTransactionIndex = array_search($processor['linkedHash'], array_column($previousTransactions, 'transactionHash'), true);

                            if ($linkedTransactionIndex !== false) {
                                $failedTransaction->linkedTransactionId = $previousTransactions[$linkedTransactionIndex]['transactionRecord']->id;
                            }
                        }

                        $failedTransaction->save(false);
                        $previousTransactions[$previousTransactionKey]['transactionRecord'] = $failedTransaction;

                        $this->saveTransactionDetails($previousTransaction['transaction'], $previousTransaction['transactionResponse'], $failedTransaction);

                        $this->failurePostBack($params, $previousTransaction, $customer);

                        if (!empty($previousTransaction['transactionResponse']->transactionId)) {
                            /** @var BaseGateway $gateway */
                            $gateway = $previousTransaction['gateway'];
                            /** @var Transaction $transaction */
                            $transaction = $previousTransaction['transaction'];
                            /** @var TransactionResponse $transactionResponse */
                            $transactionResponse = $previousTransaction['transactionResponse'];

                            ### TODO: MagtekTSYS uses refTransId/refAuthCode, all other gateways use transactionId/authCode
                            $transaction->transactionId = $transactionResponse->transactionId;
                            $transaction->refTransId = $transactionResponse->transactionId;
                            $transaction->authCode = $transactionResponse->authCode;
                            $transaction->refAuthCode = $transactionResponse->authCode;

                            $voidTransactionResponse = $gateway->voidTransaction($transaction);

                            $voidTransaction = new TransactionRecord();
                            $voidTransaction->paymentProcessorId = $previousTransaction['processorRecord']->iPaymentProcessorPk;
                            $voidTransaction->transactionId = $errorTransactionResponse->transactionId;
                            $voidTransaction->transactionStatus = $errorTransactionResponse->isError ? TransactionRecord::STATUS_FAILED : TransactionRecord::STATUS_SUCCESS;

                            # link the void transaction to the primary transaction
                            if (!empty($processor['linkedHash'])) {
                                $linkedTransactionIndex = array_search($processor['linkedHash'], array_column($previousTransactions, 'transactionHash'), true);

                                if ($linkedTransactionIndex !== false) {
                                    $voidTransaction->linkedTransactionId = $previousTransactions[$linkedTransactionIndex]['transactionRecord']->id;
                                }
                            }

                            $voidTransaction->save(false);

                            $this->saveTransactionDetails($transaction, $voidTransactionResponse, $voidTransaction);
                        }

                    }

                    return $response;
                }

                $transactionIds = [];
                $sendEmail = false;
                $historyRecords = [];
                $transactionCustomFields = [];
                foreach ($previousTransactions as $previousTransactionKey => $previousTransaction) {
                    /** @var BaseGateway $gateway */
                    $gateway = $previousTransaction['gateway'];
                    /** @var Transaction $transaction */
                    $transaction = $previousTransaction['transaction'];
                    /** @var TransactionResponse $transactionResponse */
                    $transactionResponse = $previousTransaction['transactionResponse'];
                    /** @var PaymentProcessorRecord $processorRecord */
                    $processorRecord = $previousTransaction['processorRecord'];
                    $processor = $previousTransaction['processor'];

                    if ($transactionResponse->customFields) {
                        $transactionCustomFieldData = $transactionResponse->customFields;

                        if (isset($transactionCustomFieldData['Token'])) {
                            unset($transactionCustomFieldData['Token']);
                        }

                        $transactionCustomFields[$transactionResponse->transactionId]['customFields'] = $transactionCustomFieldData;
                    }

                    $transaction->refTransId = $transactionResponse->transactionId;
                    $transaction->refAuthCode = $transactionResponse->authCode;
                    $transaction->customFields = $transactionResponse->customFields;

                    if ($transaction->paymentMethod === Pay::PAYMENT_METHOD_CARD) {
//                        ## This will be integrated after we are in full production
//                        # Log Auth from previous transaction prior to logging capture
//                        $authTransactionRecord = new TransactionRecord();
//                        $authTransactionRecord->paymentProcessorId = $processorRecord->iPaymentProcessorPk;
//                        $authTransactionRecord->transactionId = $transactionResponse->transactionId;
//                        $authTransactionRecord->transactionStatus = $transactionResponse->isError ? TransactionRecord::STATUS_FAILED : TransactionRecord::STATUS_SUCCESS;
//
//                        $authTransactionRecord->save(false);
//                        $this->saveTransactionDetails($transaction, $transactionResponse, $authTransactionRecord);
//
//                        # Do the capture and log it below

                        if ($gateway::class === 'app\components\gateways\TransFirst') {
                            $transactionResponse = $gateway->fakeAuthResponse;
                        } else {
                            $transactionResponse = $gateway->priorAuthorizeCaptureCreditCard($transaction);
                        }
                    }

                    $transactionRecord = new TransactionRecord();
                    $transactionRecord->paymentProcessorId = $processorRecord->iPaymentProcessorPk;
                    $transactionRecord->transactionId = $gateway->storeAuthCodeAsTransactionId ? $transactionResponse->authCode : $transactionResponse->transactionId;
                    $transactionRecord->transactionStatus = $transactionResponse->isError ? TransactionRecord::STATUS_FAILED : TransactionRecord::STATUS_SUCCESS;

                    # check if secondary transaction and if so, link to it
                    if ($previousTransaction['type'] === 'secondary' && !empty($processor['linkedHash'])) {
                        $linkedTransactionIndex = array_search($processor['linkedHash'], array_column($previousTransactions, 'transactionHash'), true);

                        if ($linkedTransactionIndex !== false) {
                            $transactionRecord->linkedTransactionId = $previousTransactions[$linkedTransactionIndex]['transactionRecord']->id;
                        }
                    }
//                    ## This will be integrated after we are in full production
//                    else {
//                        if(isset($authTransactionRecord)) {
//                            $transactionRecord->linkedTransactionId = $authTransactionRecord->id;
//                        }
//                    }

                    $transactionRecord->save(false);
                    $previousTransactions[$previousTransactionKey]['transactionRecord'] = $transactionRecord;

                    $this->saveTransactionDetails($transaction, $transactionResponse, $transactionRecord);

                    ### If there is an issue with the capture, add the error and return the response
                    if ($transactionResponse->isError) {
                        $response->errors[$transactionResponse->responseCode] = $transactionResponse->responseMessage;
                        return $response;
                    }

                    if ($previousTransaction['type'] === 'primary') {
                        $transactionIds[] = $gateway->storeAuthCodeAsTransactionId ? $transactionResponse->authCode : $transactionResponse->transactionId;
                    }

                    ### XPCA2-405, set $paymentDate here to ensure history records from the same transactions get the same datetime
                    $paymentDate = $this->dateTime();

                    foreach ($previousTransaction['bills'] as $paidBill) {
                        $historyAddressRecord = new HistoryAddressRecord();
                        $historyAddressRecord->setAttributes([
                            'addressLine1' => $customer->address?->addressLine1,
                            'addressLine2' => $customer->address?->addressLine2,
                            'city' => $customer->address?->city,
                            'state' => $customer->address?->state,
                            'zip' => $customer->address?->postalCode,
                            'phone' => $customer->phone?->phoneNumber
                        ]);
                        $historyAddressRecord->save(false);

                        # move bill from bill table to history table
                        $historyRecord = new HistoryRecord();

                        $attributesIntersect = array_intersect_key($paidBill['bill'], $historyRecord->getAttributes());

                        ### Remove unwanted keys from the intersected array
                        if (array_key_exists('dateCreated', $attributesIntersect)) {
                            unset($attributesIntersect['dateCreated']);
                        }
                        if (array_key_exists('email', $attributesIntersect)) {
                            unset($attributesIntersect['email']);
                        }

                        # If payment from Magtek set to null, otherwise set last 4 of payment method
                        $accountLast4 = null;
                        if ($transaction->creditCard) {
                            if (isset($creditCardParams['cardNumber'])) {
                                $accountLast4 = substr($creditCardParams['cardNumber'], -4);
                            }
                        } else {
                            $accountLast4 = substr($bankAccountParams['accountNumber'], -4);
                        }

                        # add additional attribute to the array of attributes
                        $attributes = array_merge([
                            'extraAmount' => $paidBill['bill']['interestAmount'] ?? 0,
                            'ConvenienceFee' => $paidBill['bill']['siteFees']['merchantFee'],
                            'ProcessingFee' => $paidBill['bill']['siteFees']['highestFeeToAdd'] + $paidBill['bill']['siteFees']['fixedPerBill'],
                            'XPCProfit' => $paidBill['bill']['siteFees']['xpcProfit'],
                            'ResidualProfit' => $paidBill['bill']['siteFees']['residualProfit'],
                            'ipAddress' => $params['ipAddress'] ?? Yii::$app->request->getRemoteIP(),
                            'FirstName' => $customer->firstName,
                            'LastName' => $customer->lastName,
                            'email' => $customer->emailAddress,
                            'PaymentDate' => $paymentDate,
                            'PaymentMethodType' => $pay->paymentMethod,
                            'CardBrand' => $cardBrandName,
                            'userId' => $pay->userId,
                            'iPaymentTypeFk' => $paidBill['bill']['paymentTypePk'],
                            'paymentProfileId' => $params['paymentProfileId'] ?? null,
                            'scheduledPaymentProfileId' => $params['scheduledPaymentProfileId'] ?? null,
                            'UniqueBillIdentifier' => $paidBill['bill']['uniqueBillIdentifier'] ?? null,
                            'BillDate' => $paidBill['bill']['billDate'] ?? null,
                            'historyAddressId' => $historyAddressRecord->id,
                            'bankAccountType' => $bankAccountType,
                            'accountLast4' => $accountLast4,
                            'direction' => $transaction->creditOrDebit === 'C' ? 'Out' : 'In',
                        ], $attributesIntersect);

                        $historyRecord->setAttributes($attributes);

                        # calculate totalAmount on whether site fee is absorbed or paid by consumer
                        if ($paidBill['bill']['paymentType']['iZeroSiteFee']) { // paid by consumer
                            $historyRecord->TotalPaid = $paidBill['bill']['billAmount'];
                        } else { // absorbed
                            $historyRecord->TotalPaid = $paidBill['bill']['billAmount'] + $historyRecord->ConvenienceFee + $historyRecord->ProcessingFee;
                        }

                        $historyRecord->TotalPaid += $paidBill['bill']['interestAmount'] ?? 0;

                        # set history amount to the bill amount
                        $historyRecord->Amount = $paidBill['bill']['billAmount'];
                        $historyRecord->save(false);
                        $historyRecord->link('transactions', $transactionRecord);

                        $historyRecords[] = $historyRecord;

                        ### This is specifically for SecureBancard/TSYS
                        if ($historyRecord->PaymentMethodType === Pay::PAYMENT_METHOD_CARD) {
                            $this->createSiteFeeValidationRecord($historyRecord, $pay);
                        }

                        ### Create a record in the "email" table if the payer opts in for "Posted" payment types
                        if ($paidBill['bill']['paymentType']['Type'] === PaymentTypeRecord::TYPE_POSTED) {
                            $this->createNotifyCustomerRecord($customer, $paidBill['bill']);
                        }

                        if (!$sendEmail) {
                            $sendEmail = (bool)$paidBill['bill']['paymentType']['iReceipt'];
                        }

                        ### Send SMS text message to the merchant if configured
                        if (!empty($paidBill['bill']['paymentType']['cMobileReceiptText'])) {
                            $this->sendMerchantText($paidBill['bill']['paymentType']['cMobileReceiptText'], $historyRecord);
                        }

                        foreach ($paidBill['bill']['paymentTypeLocators'] as $paymentTypeLocator) {
                            /** @var PaymentTypeLocatorRecord $paymentTypeLocatorRecord */
                            $paymentTypeLocatorRecord = PaymentTypeLocatorRecord::find()
                                ->andWhere(['iPaymentTypeFk' => $paidBill['bill']['paymentTypePk']])
                                ->andWhere(['locatorNum' => $paymentTypeLocator['locatorNum']])
                                ->andWhere([PaymentTypeLocatorRecord::tableName() . '.dateDeleted' => null])
                                ->limit(1)
                                ->one();

                            ### Bills from QBO might have more locators than configured out in the payment type
                            if ($paymentTypeLocatorRecord !== null) {
                                $locatorValue = null;
                                if (!empty($paymentTypeLocatorRecord->dropDowns)) {
                                    $dropDownIndex = array_search($paymentTypeLocator['locatorValue'], array_column($paymentTypeLocatorRecord->dropDowns, 'iMobileDropDownPk'));

                                    if ($dropDownIndex !== false) {
                                        $locatorValue = $paymentTypeLocatorRecord->dropDowns[$dropDownIndex]['cDescription'];
                                    }
                                }

                                if ($locatorValue === null) {
                                    $locatorValue = is_array($paymentTypeLocator['locatorValue']) ? reset($paymentTypeLocator['locatorValue']) : $paymentTypeLocator['locatorValue'];
                                }

                                $historyLocatorRecord = new HistoryLocatorRecord();
                                $historyLocatorRecord->setAttributes([
                                    'historyId' => $historyRecord->id,
                                    'locatorNum' => $paymentTypeLocatorRecord['locatorNum'],
                                    'locatorDescription' => $paymentTypeLocatorRecord['locatorDescription'],
                                    'locatorPrice' => $paymentTypeLocatorRecord['locatorPrice'],
                                    'locatorValue' => $locatorValue,
                                    'locatorSettings' => Json::encode([
                                        'isInventory' => $paymentTypeLocatorRecord->isInventory,
                                        'isEditable' => $paymentTypeLocatorRecord->isEditable,
                                        'isExactMatch' => $paymentTypeLocatorRecord->isExactMatch,
                                        'isHidden' => $paymentTypeLocatorRecord->isHidden,
                                        'isHiddenSensitiveData' => $paymentTypeLocatorRecord->isHiddenSensitiveData,
                                        'isRequired' => $paymentTypeLocatorRecord->isRequired,
                                        'isSearchable' => $paymentTypeLocatorRecord->isSearchable,
                                    ])
                                ]);
                                $historyLocatorRecord->save(false);
                            }
                        }

                        if ($paidBill['bill']['paymentType']['Type'] === PaymentTypeRecord::TYPE_POSTED) {
                            $this->processPostedPaidBill($paidBill['bill'], $historyRecord);
                        }

                        ### If a scheduled payment profile is being used to make the payment, do not create another scheduled payment profile
                        ### Create scheduled payment profiles for "Instant Auto-add" & "Posted Auto-add"
                        if (empty($params['scheduledPaymentProfileId']) && !empty($paidBill['bill']['paymentType']['cRecurringFrequency']) && in_array($paidBill['bill']['paymentType']['iRecurring'], [3, 4], true)) {
                            $this->processAutoAddScheduledPaymentProfile($pay, $paidBill['bill']);
                        }

                        ### Unisoft / Posted-Auto Add and LinksInsuranceLikeConfig, delete the bill after payment
                        if (!empty($params['scheduledPaymentProfileId']) && $paidBill['bill']['paymentType']['Type'] === PaymentTypeRecord::TYPE_SPP && in_array($paidBill['bill']['paymentType']['iRecurring'], [2, 4], true)) {
                            $this->deleteSppBill($paidBill['bill']);
                        }

                        ### Process PostBacks
                        $postBackUrl = '';
                        if (!empty($params['postUrl'])) {
                            $postBackUrl = $params['postUrl'];
                        } else if (!empty($paidBill['bill']['paymentType']['cAsyncCompletionURL'])) {
                            $postBackUrl = $paidBill['bill']['paymentType']['cAsyncCompletionURL'];
                        }

                        ### Test URL
                        //$postBackUrl = 'https://www.xpress-pay.com/postback/default.asp';

                        if (!empty($postBackUrl) && $paidBill['bill']['paymentType']['iAsyncCompletionType'] > 0) {
                            try {
                                $this->doPostBack($transactionResponse, $historyRecord, $transaction, $paidBill['bill'], $customer, $postBackUrl, $gateway);
                            } catch (Exception $e) {
                                Yii::error([
                                    'doPostBack' => [
                                        '$transactionResponse' => $transactionResponse,
                                        '$historyRecord' => $historyRecord->attributes,
                                        '$paidBill' => $paidBill,
                                        '$customer' => Json::encode($customer),
                                        '$postBackUrl' => $postBackUrl,
                                        '$e::class' => $e::class,
                                        '$e->getLine()' => $e->getLine(),
                                        '$e->getCode()' => $e->getCode(),
                                        '$e->getMessage()' => $e->getMessage(),
                                        '$e->getTrace()' => $e->getTrace(),
                                    ]
                                ]);
                            }
                        }
                        ### End Process PostBacks

                        ### Update 3rd Party Bills (QuickBooks Online/QBO, GA DOAS, etc.)
                        if (
                            $historyRecord->paymentType->Type === PaymentTypeRecord::TYPE_GLOBAL_AXCESS &&
                            (!empty($historyRecord->paymentType->iPostedBillAPI) || !empty($historyRecord->paymentType->organization->iPostedBillAPI))
                        ) {

                            try {
                                $this->update3rdPartyBills($historyRecord, $paidBill, $transactionRecord);
                            } catch (Exception $e) {
                                Yii::error([
                                    'update3rdPartyBills' => [
                                        '$historyRecord' => $historyRecord->attributes,
                                        '$paidBill' => $paidBill,
                                        '$transactionRecord' => $transactionRecord->attributes,
                                        '$e::class' => $e::class,
                                        '$e->getLine()' => $e->getLine(),
                                        '$e->getCode()' => $e->getCode(),
                                        '$e->getMessage()' => $e->getMessage(),
                                        '$e->getTrace()' => $e->getTrace(),
                                    ]
                                ]);
                            }
                        }
                    }
                }

                ### Send out email receipt to person making the payment, but not when the payment is a "scheduled payment" or a "pay out"
                $bCredit = array_key_exists('creditOrDebit', $params) && $params['creditOrDebit'] === 'C';
                if (empty($params['scheduledPaymentProfileId']) && $sendEmail && !empty($customerParams['emailAddress']) && !$bCredit && !$isOutGoingPayment) {
                    $this->sendPayerReceiptEmail($historyRecords, $pay, $customerParams['emailAddress']);
                }

                $merchantEmailAddresses = $this->calcMerchantEmails($historyRecords);

                ### Send out copy of email receipt to the merchant if configured
                if (!empty($merchantEmailAddresses)) {
                    $this->sendMerchantReceiptEmail($historyRecords, $pay, $merchantEmailAddresses, $bCredit);
                }

                $response->success = true;
                $response->object = 'transaction';
                $response->data['transaction'] = 'Transaction completed';

                if ($pay->includeHistoryIds) {
                    $arrHistory = [];
                    foreach ($historyRecords as $historyRecord) {
                        $arrHistory[] = ['historyId' => $historyRecord->id, 'transactionIds' => array_column($historyRecord->transactions, 'id')];
                    }

                    $response->data['historyRecords'] = $arrHistory;
                    $response->data['transactionCustomData'] = $transactionCustomFields;
                }

                $response->data['transactionId'] = implode(', ', $transactionIds);
            }
        }

        return $response;
    }

    /**
     * @param array $params
     * @param Response $response
     * @return Response
     * @throws InvalidConfigException
     * @throws YiiDbException
     */
    protected function runFraudDetector(array $params, Response $response): Response
    {
        $headers = Yii::$app->request->getHeaders();
        $userAgent = Yii::$app->request->userAgent;
        $ipAddress = trim((string)($params['ipAddress'] ?? ''));

        // Validate incoming user data
        $fullName = isset($params['customer']['fullName']) ? trim((string)$params['customer']['fullName']) : '';
        $emailAddress = isset($params['customer']['emailAddress']) ? trim((string)$params['customer']['emailAddress']) : '';
        $phoneNumber = isset($params['customer']['phone']['phoneNumber']) ? trim((string)$params['customer']['phone']['phoneNumber']) : '';
        $address = $params['customer']['address'] ?? [];

        $paymentTypePks = array_column($params['bills'] ?? [], 'paymentTypePk');

        // Prepare fraud detector
        $detector = new ScoringFraudDetector();

        // ----------------------------------------------------------------------------------------
        // List overrides: returns true = whitelisted, false = blacklisted, null = normal
        // ----------------------------------------------------------------------------------------
        $listDecision = $detector->applyListOverrides($ipAddress, $paymentTypePks);

        // Block immediately if blacklisted.
        if ($listDecision === false) {
            return $this->fraudErrorResponse($response);
        }

        // Check if fraud detection is enabled and run checks if not whitelisted
        if ($detector->fraudDetectionEnabled && $listDecision !== true) {
            // ---- Early velocity gate (inexpensive read-only check)
            if ($detector->isVelocityExceededByPaymentTypePk(self::FRAUD_FAIL_LIMIT, self::FRAUD_WINDOW_SEC, $paymentTypePks)) {
                $detector->checkVelocityAndBlackListByPaymentTypePk($ipAddress, self::FRAUD_FAIL_LIMIT, self::FRAUD_WINDOW_SEC, $paymentTypePks);

                if ($detector->fraudMode === ScoringFraudDetector::FRAUD_MODE_ENFORCE) {
                    return $this->fraudErrorResponse($response);
                }
            }

            // Check payment type requirements for fields
            /*
             * Dropdown option value for these fields:
             * 2 - Hidden
             * 1 - Shown and required
             * 0 - Shown and not required
             */
            $requirements = PaymentTypeRecord::find()
                ->select([
                    new Expression("
                    MAX(CASE
                        WHEN iEmailRequired = 2 THEN 2
                        WHEN iEmailRequired = 1 THEN 1
                        ELSE 0 END) AS emailStatus"),
                    new Expression("
                    MAX(CASE
                        WHEN iPhoneRequired = 2 THEN 2
                        WHEN iPhoneRequired = 1 THEN 1
                        ELSE 0 END) AS phoneStatus"),
                    new Expression("
                    MAX(CASE
                        WHEN iAddressRequired = 2 THEN 2
                        WHEN iAddressRequired = 1 THEN 1
                        ELSE 0 END) AS addressStatus"),
                    new Expression("
                    MAX(CASE
                        WHEN iZipRequired = 2 THEN 2
                        WHEN iZipRequired = 1 THEN 1
                        ELSE 0 END) AS postalCodeStatus"),
                ])
                ->where(['iPaymentTypePk' => array_unique($paymentTypePks)])
                ->asArray()
                ->one();

            $addressRequirements = array_intersect_key($requirements, array_flip(['addressStatus', 'postalCodeStatus']));

            // Base (inexpensive) checks
            $baseChecks = [
                ['method' => 'verifyFullName', 'args' => [$fullName]],
                ['method' => 'verifyAddress', 'args' => [$address, $addressRequirements]],
                ['method' => 'verifyEmailAddress', 'args' => [$emailAddress, $requirements['emailStatus']]],
                ['method' => 'verifyPhoneNumber', 'args' => [$phoneNumber, $requirements['phoneStatus']]],
                ['method' => 'verifyRequestEnvironment', 'args' => [$ipAddress, $userAgent, $headers->toArray()]],
            ];

            // Expensive (paid API) checks
            $expChecks = [
                ['method' => 'verifyAddressGeoCode', 'args' => [$address, $addressRequirements]],
            ];

            // ---- Metadata for persistence
            $metadata = [
                'ip' => $ipAddress,
                'userAgent' => $userAgent,
                'paymentTypePks' => $paymentTypePks,
            ];

            // Run staged evaluation (margin = 1.0 point below a threshold)
            $result = $detector->evaluateWithEscalation(
                $baseChecks,
                $expChecks,
                $metadata
            );

            if (!$result['isValid']) {
                $detector->checkVelocityAndBlackListByScore($ipAddress, self::FRAUD_FAIL_LIMIT, self::FRAUD_WINDOW_SEC, $paymentTypePks);

                if ($detector->fraudMode === ScoringFraudDetector::FRAUD_MODE_ENFORCE) {
                    return $this->fraudErrorResponse($response);
                }
            }
        }

        // All checks are valid, proceed with payment
        $response->success = true;
        return $response;
    }

    /**
     * Determine if the processorMode is not "isDemo"
     *
     * @param HistoryRecord $historyRecord
     * @return bool
     */
    protected function calcDemoMode(HistoryRecord $historyRecord): bool
    {
        $isDemo = 'isDemo';

        ### Attempt to fix 'Attempt to read property "processorMode" on null in'
        $creditCardProcessor = $historyRecord->paymentType->merchantInfo->creditCardProcessor;
        $checkProcessor = $historyRecord->paymentType->merchantInfo->checkProcessor;
        $paymentMethod = $historyRecord->paymentType->paymentMethod;

        if ($paymentMethod === 1 && $creditCardProcessor !== null) {
            return $creditCardProcessor->processorMode !== $isDemo;
        }

        if ($paymentMethod === 2 && $checkProcessor !== null) {
            return $checkProcessor->processorMode !== $isDemo;
        }

        if ($paymentMethod === 3 && $creditCardProcessor !== null && $checkProcessor !== null) {
            $bCard = $creditCardProcessor->processorMode !== $isDemo;
            $bCheck = $checkProcessor->processorMode !== $isDemo;
            return $bCard && $bCheck;
        }

        return false;
    }

    /**
     * Send an async post upon payment failure attempt if configured
     *
     * @param array $params
     * @param array $previousTransaction
     * @param Customer $customer
     * @return void
     */
    protected function failurePostBack(array $params, array $previousTransaction, Customer $customer): void
    {
        /** @var TransactionResponse $transactionResponse */
        $transactionResponse = $previousTransaction['transactionResponse'];

        foreach ($previousTransaction['bills'] as $each) {
            $unpaidBill = $each['bill'];
            $paymentType = $unpaidBill['paymentType'];

            ### XPSV3-240: Yii2 upgrade to 2.0.48+ changed the value type of int to bool for the "bit" column
            if ($paymentType['iAsyncCompletionType'] > 0 && $paymentType['iCompletionProcessing']) {
                // $postBackUrl = 'https://www.xpress-pay.com/postback/default.asp';
                $postBackUrl = '';
                if (!empty($params['postUrl'])) {
                    $postBackUrl = $params['postUrl'];
                } else if (!empty($paymentType['cAsyncCompletionURL'])) {
                    $postBackUrl = $paymentType['cAsyncCompletionURL'];
                }

                if (empty($postBackUrl)) {
                    Yii::error(['Empty $postBackUrl' => [
                        '$params' => $params,
                        '$previousTransaction' => $previousTransaction,
                        '$customer' => $customer,
                    ]], __METHOD__);
                } else {
                    switch ($paymentType['iAsyncCompletionType']) {
                        case 4:
                            # Generic Async v2
                            ### v2 Spec below
                            $postBackParams = GenericPostBackV2::createFailurePostBackParams($unpaidBill, $transactionResponse, $customer);
                            $postBack = new GenericPostBackV2($postBackUrl);
                            break;
                        case 8:
                        default:
                            # Generic Async
                            ### v3 Spec below
                            $postBackParams = GenericPostBack::createFailurePostBackParams($unpaidBill, $transactionResponse, $customer, $params);

                            $postBack = new GenericPostBack($postBackUrl);
                            if (!empty($paymentType['asyncPostBackAuthToken'])) {
                                $postBack->setHeaders(['Authorization' => "Token {$paymentType['asyncPostBackAuthToken']}"]);
                            }
                            break;
                    }

                    $postBack->setParams($postBackParams);
                    $postBack->post();
                }
            }
        }
    }

    /**
     * Conditionally create a record in the siteFeeValidation table
     *
     * Criteria for saving to siteFeeValidation table
     * Payment type method used is card/credit cards
     * Organization is "Dual deposit"
     * Merchant Provider = "Secure Bancard" / iMerchantProviderPk === 10
     * Gateway credentials for primary and secondary are identical
     *
     * @param HistoryRecord $historyRecord
     * @param Pay $pay
     * @return void
     * @throws YiiDbException
     */
    protected function createSiteFeeValidationRecord(HistoryRecord $historyRecord, Pay $pay): void
    {
        $bCardPaymentMethodType = $historyRecord->PaymentMethodType === Pay::PAYMENT_METHOD_CARD;
        $paymentType = $historyRecord->paymentType;
        $bDualDeposit = $paymentType->organization->iSplitCharge === 2;

        if ($bCardPaymentMethodType && $bDualDeposit) {
            $merchantInfo = $paymentType->merchantInfo;
            $bSecureBancard = $merchantInfo->merchantProviderCC->iMerchantProviderPk === 10;
            $bPaymentProcessor = $merchantInfo->creditCardProcessor->iPaymentProcessorPk === 47;

            $cardFields = $merchantInfo->creditCardProcessor->getProcessorFields()
                ->joinWith(['fieldValues' => static function (BaseQuery $query) use ($merchantInfo) {
                    $query->alias('fieldValues')
                        ->andWhere(['fieldValues.iMerchantInfoFk' => $merchantInfo->iMerchantInfoPk]);
                }])->all();

            [$primaryForm, $secondaryForm] = ProcessorCreditCard::createForms($cardFields);

            $mainHash = $merchantInfo->calculateHash(ProcessorCreditCard::class, $primaryForm);
            $dualHash = $merchantInfo->calculateHash(ProcessorCreditCard::class, $secondaryForm);

            if ($bSecureBancard && $mainHash === $dualHash) {
                ### AKA Merchant Fee === $historyRecord->ConvenienceFee
                $siteFee = $historyRecord->ConvenienceFee + $historyRecord->ProcessingFee;
                $transactionDetails = $historyRecord->transactions[0]->transactionDetails;

                # Checks if processor is magtek
                if ($bPaymentProcessor) {
                    $authCode = array_column($transactionDetails, 'metaValue', 'metaKey')['refAuthCode'] ?? null;

                    $cardLast = null;
                    $customFields = Json::decode(array_column($transactionDetails, 'metaValue', 'metaKey')['customFields'] ?? null);

                    if (array_key_exists('cardNumber', $customFields)) {
                        $cardLast = $customFields['cardNumber'];
                    }
                } else {
                    $authCode = array_column($transactionDetails, 'metaValue', 'metaKey')['authCode'] ?? null;
                    $cardLast = substr($pay->creditCard->cardNumber, -4);
                }

                $siteFeeValidation = new SiteFeeValidationRecord([
                    'merchantProviderFk' => $merchantInfo->merchantProviderCC->iMerchantProviderPk,
                    'paymentProcessorFk' => $merchantInfo->creditCardProcessor->iPaymentProcessorPk,
                    'merchantId' => $paymentType->cMerchantID,
                    'cardLast' => Helper::encryptData($cardLast),
                    'historyId' => $historyRecord->id,
                    'authCode' => $authCode,
                    'siteFee' => $siteFee,
                    'amount' => $historyRecord->TotalPaid,
                    'dateTime' => $historyRecord->PaymentDate,
                ]);

                $siteFeeValidation->save();

                if ($siteFeeValidation->hasErrors()) {
                    Yii::info([
                        '$siteFeeValidation->errors' => $siteFeeValidation->errors,
                    ], __METHOD__);
                }

                if (empty($paymentType->cMerchantID)) {
                    Yii::info([
                        'createSiteFeeValidationRecord' => '$paymentType->cMerchantID was empty',
                        'iPaymentTypePk' => $paymentType->iPaymentTypePk,
                    ], __METHOD__);
                }
            }
        }
    }

    /**
     * TODO: Use values connected from the history record instead of all of the various sources
     * @param TransactionResponse $transactionResponse
     * @param HistoryRecord $historyRecord
     * @param Transaction $transaction
     * @param array $bill
     * @param Customer $customer
     * @param string $postBackUrl
     * @param BaseGateway $gateway
     * @return void
     */
    protected function doPostBack(TransactionResponse $transactionResponse, HistoryRecord $historyRecord, Transaction $transaction, array $bill, Customer $customer, string $postBackUrl, BaseGateway $gateway): void
    {
        ### TODO: We should change these to how we want them now, and then maybe have a configuration to control which version
        $postBackParams = [
            'transactionId' => $gateway->storeAuthCodeAsTransactionId ? $transactionResponse->authCode : $transactionResponse->transactionId,
            'paymentType' => $historyRecord->PaymentMethodType,
            'uniqueBillIdentifier' => $historyRecord->UniqueBillIdentifier,
            'locators' => $bill['paymentTypeLocators'] ?? [],
            'billAmount' => $historyRecord->Amount,
            'totalPaid' => $historyRecord->TotalPaid,
            'siteFee' => $historyRecord->paymentType->iZeroSiteFee ? 0.0 : $historyRecord->ConvenienceFee + $historyRecord->ProcessingFee,
            'extraAmount' => $historyRecord->extraAmount,
            'approvalCode' => $historyRecord->PaymentMethodType === 'CreditCard' ? '1' : 'A',
            'fullName' => $customer->fullName,
            'FirstName' => $customer->firstName,
            'LastName' => $customer->lastName,
            'addressLine1' => $customer->address->addressLine1,
            'City' => $customer->address->city,
            'State' => $customer->address->state,
            'postalCode' => $customer->address->postalCode,
            'emailAddress' => $customer->emailAddress,
            'phoneNumber' => $customer->phone->phoneNumber,
            'accountLast4' => substr($historyRecord->PaymentMethodType === 'CreditCard' ? $transaction->creditCard->cardNumber : $transaction->bankAccount->accountNumber, -4),
            'cardBrand' => $historyRecord->CardBrand,
        ];

        switch ($bill['paymentType']['iAsyncCompletionType']) {
            case 3:
                # Beacon Async
                $postBack = new BeaconPostBack($postBackUrl);
                $postBackParams = BeaconPostBack::mapProperties($postBackParams);

                [$companyId, $divisionId] = array_pad(explode('|', $postBackParams['l4'] ?? ''), 2, '');
                $postBack->setHeaders([
                    'AuthToken' => $bill['paymentType']['cCompletionKey'],
                    'BsCompanyId' => $postBackParams['l3'] ?? '',
                    'CompanyId' => $companyId,
                    'DivisionId' => $divisionId,
                ]);
                unset($postBackParams['l3'], $postBackParams['l4']);

                break;
            case 7:
                # Spectrum Async
                $tokenParams = [
                    'token' => $bill['paymentType']['cCompletionKey'],
                    'username' => $bill['paymentType']['cCompletionUserName'],
                    'password' => $bill['paymentType']['cCompletionPassword'],
                ];
                $postBack = new SpectrumPostBack($postBackUrl . 'api/Token');
                $postBack->setParams($tokenParams);
                $responseToken = Json::decode($postBack->post());
                $token = $responseToken['data']['attributes']['token'];

                $postBack = new SpectrumPostBack($postBackUrl . 'api/OnlineReceipt');
                $postBack->setHeaders([
                    'Authorization' => 'Bearer ' . $token,
                ]);
                $postBackParams = SpectrumPostBack::mapProperties($postBackParams);
                break;
            case 9:
                # IMTApps
                $tokenParams = IMTApps::getAuthTokenParams($bill['paymentType']);
                $authUrl = YII_ENV_PROD ? IMTApps::AUTH_API_PRODUCTION_REQUEST_URI : IMTApps::AUTH_API_DEVELOPMENT_REQUEST_URI;

                $authPostBack = new IMTApps($authUrl . '/sessions/');
                $authPostBack->setParams($tokenParams);
                $authPostBack->setHeaders([
                    'Content-Type' => IMTApps::MIME_TYPE,
                ]);

                $responseAuthToken = Json::decode($authPostBack->post());

                $token = $responseAuthToken['data']['attributes']['auth'];

                $postBackUrl = YII_ENV_PROD ? IMTApps::PAYMENT_API_PRODUCTION_REQUEST_URI : IMTApps::PAYMENT_API_DEVELOPMENT_REQUEST_URI;
                ### TODO: I think 7732 is the "Dev" company_id, https://adminv3.xpress-pay.com/payment-type/update?id=3071
                // $companyId = '/7732';
                ### TODO: Not sure if companyId will be dynamic based on login credentials
                $companyId = DIRECTORY_SEPARATOR . $responseAuthToken['data']['attributes']['company_number'];

                $postBack = new IMTApps("$postBackUrl$companyId/api/payments/");
                $postBack->setHeaders([
                    'Accept' => IMTApps::MIME_TYPE,
                    'Cookie' => "auth=$token",
                    'Content-Type' => IMTApps::MIME_TYPE,
                ]);

                $postBackParams = IMTApps::mapProperties($postBackParams);
                break;
            case 4:
                # Generic Async v2
                $postBack = new GenericPostBackV2($postBackUrl);
                $postBackParams = GenericPostBackV2::mapProperties($postBackParams);
                break;
            case 10:
                # Generic Async with Last 4
                $postBack = new GenericPostBackWithLast4($postBackUrl);

                if (!empty($bill['paymentType']['asyncPostBackAuthToken'])) {
                    $postBack->setHeaders(['Authorization' => "Token {$bill['paymentType']['asyncPostBackAuthToken']}"]);
                }

                $postBackParams = GenericPostBackWithLast4::mapProperties($postBackParams);
                break;
            case 8:
            default:
                # Generic Async
                $postBack = new GenericPostBack($postBackUrl);

                if (!empty($bill['paymentType']['asyncPostBackAuthToken'])) {
                    $postBack->setHeaders(['Authorization' => "Token {$bill['paymentType']['asyncPostBackAuthToken']}"]);
                }

                $postBackParams = GenericPostBack::mapProperties($postBackParams);
                break;
        }

        if (!empty($bill['paymentType']['aSyncErrorHandlingEmail'])) {
            $postBack->setOptions(['aSyncErrorHandlingEmail' => $bill['paymentType']['aSyncErrorHandlingEmail']]);
        }

        $postBack->setParams($postBackParams);
        $postBack->post();
    }

    /**
     * Store details of the transaction
     *
     * @param Transaction $transaction
     * @param TransactionResponse $transactionResponse
     * @param TransactionRecord $transactionRecord
     * @return void
     * @throws Exception
     * @throws YiiDbException
     */
    protected function saveTransactionDetails(Transaction $transaction, TransactionResponse $transactionResponse, TransactionRecord $transactionRecord): void
    {
        $formatter = new Formatter();
        $requestDetails = $formatter->formatToArray($transaction, 'object');

        ### FYI, WorldPay requires the same card number to do a refund/void.  We might need to save the encrypted value instead of the masked value.
        $oMagoo = new Magoo();
        $oMagoo->pushCreditCardMask();
        $oMagooArray = new MagooArray($oMagoo);
        $requestDetails = $oMagooArray->getMasked($requestDetails);

        ### Sanitize the security code
        if (array_key_exists('creditCard', $requestDetails) && Helper::isJson($requestDetails['creditCard'])) {
            $creditCard = Json::decode($requestDetails['creditCard']);
            if (array_key_exists('securityCode', $creditCard)) {
                $creditCard['securityCode'] = str_repeat('*', strlen($creditCard['securityCode']));
            }
            $requestDetails['creditCard'] = Json::encode($creditCard);
        }

        $responseDetails = [];
        if (!empty($transactionResponse->rawResponse)) {
            $responseDetails = $formatter->formatToArray($transactionResponse->rawResponse, $transactionResponse->rawResponseFormat);
        }

        $transactionId = $transactionRecord->id;

        $requestInsert = [];
        foreach ($requestDetails as $k => $v) {
            $requestInsert[] = [$transactionId, 'Request', $k, $v];
        }

        $responseInsert = [];
        foreach ($responseDetails as $k => $v) {
            ### Don't save blank strings
            if ($v !== '') {
                $responseInsert[] = [$transactionId, 'Response', $k, is_array($v) ? Json::encode($v) : $v];
            }
        }

        $batchInsert = array_merge($requestInsert, $responseInsert);

        Yii::$app->db->createCommand()
            ->batchInsert(TransactionDetailRecord::tableName(), ['transactionId', 'method', 'metaKey', 'metaValue'], $batchInsert)
            ->execute();
    }

    /**
     * @param Customer $customer
     * @param array $bill
     * @return void
     * @throws YiiDbException
     */
    protected function createNotifyCustomerRecord(Customer $customer, array $bill): void
    {
        $uid = Helper::calcUniqueId($bill);

        if ($uid !== null && $customer->notifyMe && !empty($customer->emailAddress)) {
            $emailRecord = EmailRecord::findOne([
                'iOrganizationFk' => $bill['organizationPk'],
                'cFullName' => $customer->fullName,
                'cEmail' => $customer->emailAddress,
                'UniqueBillIdentifier' => $uid,
            ]);

            if ($emailRecord) {
                $emailRecord->touch('dateUpdated');
            } else {
                $newEmailRecord = new EmailRecord([
                    'iOrganizationFk' => $bill['organizationPk'],
                    'cFullName' => $customer->fullName,
                    'cEmail' => $customer->emailAddress,
                    'UniqueBillIdentifier' => $uid,
                ]);

                if ($newEmailRecord->validate()) {
                    $newEmailRecord->save(false);
                } else {
                    Yii::error([
                        '$paidBill["bill"]' => $bill,
                        '$newEmailRecord->attributes' => $newEmailRecord->attributes,
                        '$newEmailRecord->errors' => $newEmailRecord->errors,
                        '$customer->attributes' => $customer->attributes,
                    ], __METHOD__);
                }
            }
        }
    }

    /**
     * @param string $cMobileReceiptText
     * @param HistoryRecord $historyRecord
     * @return void
     */
    protected function sendMerchantText(string $cMobileReceiptText, HistoryRecord $historyRecord): void
    {
        $twilio = new Twilio();

        ### Using text area
        $phoneNumbers = explode("\r\n", trim($cMobileReceiptText));

        ### Using text input with commas
        // $phoneNumbers = explode(',', $bill['bill']['paymentType']['cMobileReceiptText']);
        // $phoneNumbers = array_filter(array_map('trim', $phoneNumbers));

        try {
            $moduleViewPath = Yii::$app->getModule($this->moduleId)?->getViewPath();
            $view = Yii::$app->getView()->renderPhpFile($moduleViewPath . '/mail/pay/sms-receipt.php', [
                'historyRecord' => $historyRecord,
            ]);
            foreach ($phoneNumbers as $phoneNumber) {
                $twilio->sendText($phoneNumber, $view);
            }
        } catch (Exception|Throwable $e) {
            Yii::info([
                '$phoneNumbers' => $phoneNumbers,
                '$historyRecord->attributes' => $historyRecord->attributes,
                '$e::class' => $e::class,
                '$e->getMessage()' => $e->getMessage(),
                '$e->getCode()' => $e->getCode(),
                '$e->getLine()' => $e->getLine(),
                '$e->getTrace()' => $e->getTrace(),
            ], __METHOD__);
        }
    }

    /**
     * Use BinCheck to check the BIN/IIN
     *
     * @param CreditCard $creditCard
     * @param array $paymentTypeRecords
     * @return void
     */
//    protected function binLookupBinCheck(CreditCard $creditCard, array $paymentTypeRecords): void
//    {
//        $binCheckResponse = \app\components\BinCheck::request($creditCard->cardNumber);
//
//        if ($binCheckResponse instanceof stdClass && $binCheckResponse->success) {
//            ### Values for "funding"
//            ### "credit", "debit", "prepaid"
//            if ($binCheckResponse->BIN->type === 'CREDIT') {
//                $creditCard->addError('cardNumber', 'Only debit cards are accepted by this merchant. The bank reports that the number provided is for a credit card.');
//            }
//        }
//    }

    /**
     * Use IINList to check the BIN/IIN
     *
     * @param CreditCard $creditCard
     * @param array $paymentTypeRecords
     * @return void
     */
//    protected function binLookup(CreditCard $creditCard, array $paymentTypeRecords): void
//    {
//        $iinListResponse = IINList::request($creditCard->cardNumber);
//
//        if ($iinListResponse instanceof stdClass && !empty($iinListResponse->_embedded->cards)) {
//            ### Values for "funding"
//            ### "credit", "debit", "prepaid"
//            if (count($iinListResponse->_embedded->cards) === 1) {
//                if ($iinListResponse->_embedded->cards[0]->account->funding === 'credit') {
//                    $creditCard->addError('cardNumber', 'Only debit cards are accepted by this merchant. The bank reports that the number provided is for a credit card.');
//                }
////            } else {
////                Yii::info([
////                    '$truncatedCardNumber' => substr($creditCard->cardNumber, 0, 8),
////                    '$iinListResponse' => Json::decode(Json::encode($iinListResponse)),
////                ], __METHOD__);
//            }
//        }
//    }

    /**
     * Use IINList & BinCheck to check the BIN/IIN
     *
     * @param CreditCard $creditCard
     * @return void
     */
    protected function binLookupCombined(CreditCard $creditCard): void
    {
        $results = [];

        $binCheckResponse = BinCheck::request($creditCard->cardNumber);

        if ($binCheckResponse instanceof stdClass && $binCheckResponse->success) {
            ### Values for "type"
            ### "CREDIT", "DEBIT", "PREPAID"
            $results[] = ['debitCheck' => strtolower($binCheckResponse->BIN->type)];
        }

        $iinListResponse = IINList::request($creditCard->cardNumber);

        if ($iinListResponse instanceof stdClass && !empty($iinListResponse->_embedded->cards)) {
            foreach ($iinListResponse->_embedded->cards as $card) {
                ### Values for "funding"
                ### "credit", "debit", "prepaid"
                $results[] = ['debitCheck' => strtolower($card->account->funding)];
                if (isset($card->product->name) && stripos($card->product->name, 'debit') !== false) {
                    $results[] = ['debitCheck' => 'debit'];
                }
            }
        }

        if (!empty($results)) {
            $bDebitFound = (bool)array_filter(
                array_column($results, 'debitCheck'),
                static fn($result) => $result !== 'credit'
            );

            if (!$bDebitFound) {
                $creditCard->addError('cardNumber', 'Only debit cards are accepted by this merchant. The bank reports that the number provided is for a credit card.');
            }
        }

    }

    /**
     * @param array $historyRecords
     * @param Pay $pay
     * @param array $merchantEmailAddresses
     * @param bool $credit
     * @return void
     */
    protected function sendMerchantReceiptEmail(array $historyRecords, Pay $pay, array $merchantEmailAddresses, bool $credit): void
    {
        try {
            $mailer = Yii::$app->mailer;
            $mailer->htmlLayout = 'layouts/html-receipt';
            $mailer->compose('/mail/pay/receipt', [
                'historyRecords' => $historyRecords,
                'creditCard' => $pay->creditCard,
                'bankAccount' => $pay->bankAccount,
                'credit' => $credit,
            ])
                ->setFrom(Constants::NO_REPLY_EMAIL_ADDRESS)
                ->setTo($merchantEmailAddresses)
                ->setSubject($credit ? 'Your outgoing payment receipt from Xpress-pay.com' : 'Your receipt from Xpress-Pay.com')
                ->send();
        } catch (Exception|Throwable $e) {
            Yii::error([
                '$merchantEmailAddresses' => $merchantEmailAddresses,
                '$e::class' => $e::class,
                '$e->getMessage()' => $e->getMessage(),
                '$e->getLine()' => $e->getLine(),
                '$e->getCode()' => $e->getCode(),
                '$e->getTrace()' => $e->getTrace(),
            ], __METHOD__);
        }
    }

    /**
     * @param array $bill
     * @return void
     */
    protected function deleteSppBill(array $bill): void
    {
        ### Bills passed to this service by ???
        if (array_key_exists('iBillPk', $bill)) {
            BillRecord::deleteAll(['iBillPk' => $bill['iBillPk']]);
        }

        ### Bills passed to this service by Scheduled Payments
        if (array_key_exists('billPk', $bill)) {
            BillRecord::deleteAll(['iBillPk' => $bill['billPk']]);
        }
    }

    /**
     * @param array $historyRecords
     * @param Pay $pay
     * @param string $emailAddress
     * @return void
     */
    protected function sendPayerReceiptEmail(array $historyRecords, Pay $pay, string $emailAddress): void
    {
        try {
            $mailer = Yii::$app->mailer;
            $mailer->htmlLayout = 'layouts/html-receipt';
            $mailer->compose('/mail/pay/receipt', [
                'historyRecords' => $historyRecords,
                'creditCard' => $pay->creditCard,
                'bankAccount' => $pay->bankAccount,
                'credit' => false,
            ])
                ->setFrom(Constants::NO_REPLY_EMAIL_ADDRESS)
                ->setTo($emailAddress)
                ->setSubject('Your receipt from Xpress-Pay.com')
                ->send();
        } catch (Exception|Throwable $e) {
            Yii::error([
                '$emailAddress' => $emailAddress,
                '$e::class' => $e::class,
                '$e->getMessage()' => $e->getMessage(),
                '$e->getLine()' => $e->getLine(),
                '$e->getCode()' => $e->getCode(),
                '$e->getTrace()' => $e->getTrace(),
            ], __METHOD__);
        }
    }

    /**
     * @param array $paymentTypeLocators
     * @return array
     */
    protected function createMappedLocators(array $paymentTypeLocators): array
    {
        $locators = Helper::array_columns($paymentTypeLocators, ['locatorNum', 'locatorValue']);

        ### separate concept to convert locatorNum to int and locatorValue to string
        return array_map(
            static function ($locator) {
                $locator['locatorNum'] = (int)$locator['locatorNum'];
                $locator['locatorValue'] = reset($locator['locatorValue']);

                return $locator;
            },
            $locators
        );
    }

    /**
     * @param HistoryRecord $historyRecord
     * @param array $paidBill
     * @param TransactionRecord $transactionRecord
     * @return void
     * @throws HttpException
     * @throws IdsException
     * @throws InvalidConfigException
     * @throws NotFoundHttpException
     * @throws SdkException
     * @throws UnAuthorizedException
     * @throws YiiDbException
     */
    protected function update3rdPartyBills(HistoryRecord $historyRecord, array $paidBill, TransactionRecord $transactionRecord): void
    {
        $paymentTypeId = $historyRecord->paymentType->iPaymentTypePk;

        if (empty($historyRecord->paymentType->iPostedBillAPI)) {
            $object = $historyRecord->paymentType->organization;
            $objectId = $object->iOrganizationPk;
            $level = Retriever::ORGANIZATION_LEVEL;
        } else {
            $object = $historyRecord->paymentType;
            $objectId = $object->iPaymentTypePk;
            $level = Retriever::PAYMENT_TYPE_LEVEL;
        }

        $username = $object->cPostedBillAPIUsername;
        $password = $object->cPostedBillAPIPassword;

        $invoice = [
            'invoiceNumber' => $paidBill['bill']['billPk'],
            'amountPaid' => $paidBill['bill']['billAmount'],
            'transactionId' => $transactionRecord->transactionId,
        ];

        $additionalData = [
            'paymentType' => $historyRecord->PaymentMethodType === Pay::PAYMENT_METHOD_CARD ? 'creditCard' : 'checking',
            'siteFees' => $paidBill['siteFee'],
        ];

        switch ($object->iPostedBillAPI) {
            # Commented out, not being used currently.
            # Central Service Assoc
//            case 1:
//                $csa = new CentralServiceAssoc();
//                $csa->setCredentials($username, $password, $id);
//
//                $invoice = $invoices[0];
//                // $payment = $csa->payBill($invoice, $additionalData);
//                $csa->payBill($invoice, $additionalData);
//
//                break;
            # QuickBooks Online
            case 2:
                $username = Yii::$app->params['quickbooks']['clientId'];
                $password = Yii::$app->params['quickbooks']['clientSecret'];

                $quickBooks = new QuickBooks();
                $quickBooks->setAccessTokenLevel($level);
                $quickBooks->setCredentials($username, $password, $objectId);
                $quickBooks->setPaymentTypeId($paymentTypeId);

                $isAuthorized = $quickBooks->isAuthorized();

                if ($isAuthorized) {
                    $additionalData['depositAccountId'] = $object->cPostedBillAPIQuickBooksAccountId;
                    $additionalData['itemId'] = $object->cPostedBillAPIQuickBooksItemId;
                    $quickBooks->payBill($invoice, $additionalData);
                }

                break;
            # GA Doas
            case 3:
                $GADoas = new GADoas();
                $GADoas->setCredentials($username, $password, $objectId);
                $GADoas->setPaymentTypeId($paymentTypeId);
                $GADoas->payBill($invoice);
                break;
            # Commented out, not being used currently.
            # Sagitta
//          case 5:
//                $apiUrl = $object->cPostBillAPIUrl;
//                $additionalPassword = $object->cPostBillAPIExtraLoginParam1;
//                $Sagitta = new Sagitta();
//                $Sagitta->setCredentials($username, $password, $objectId, $apiUrl, $additionalPassword);
//                $Sagitta->setPaymentTypeId($paymentType->iPaymentTypePk);
//                foreach ($invoices as $invoice) {
//                    $Sagitta->payBill($invoice, $additionalData);
//                }
//                break;
            default:
                break;
        }
    }

    /**
     * @param array $bill
     * @param HistoryRecord $historyRecord
     * @return void
     * @throws YiiDbException
     * @throws InvalidConfigException
     */
    protected function processPostedPaidBill(array $bill, HistoryRecord $historyRecord): void
    {
        $billRecord = BillRecord::find()
            ->joinWith(['billLocators.paymentTypeLocator'])
            ->andWhere(['iBillPk' => $bill['iBillPk']])
            ->one();

        if ($billRecord) {
            ### XPSV3-120: Do not delete the bill if a partial payment was made,
            ### use weak comparison because $billRecord->Amount is a string and $bill['billAmount'] could be a string/int/float
            /** @noinspection TypeUnsafeComparisonInspection */
            if ($billRecord->Amount != $bill['billAmount'] || $bill['paymentType']['iTrackZeroBills']) {
                $billRecord->Amount -= $bill['billAmount'];

                # generate bill hash based on the following fields [UniqueBillIdentifier, Amount, Interest, email, iPaymentTypeFk and all locators]
                $hashCols = ['UniqueBillIdentifier', 'Amount', 'Interest', 'email', 'iPaymentTypeFk'];

                $billLocators = ArrayHelper::map($billRecord->billLocators, static fn($locator) => 'locator' . $locator->paymentTypeLocator->locatorNum, 'locatorValue');
                $dataArray = array_merge($billRecord->attributes, $billLocators);

                $billRecord->billHash = Helper::hashArray(array_filter(
                    $dataArray,
                    static fn($key) => (in_array($key, $hashCols, true) || str_starts_with($key, 'locator')),
                    ARRAY_FILTER_USE_KEY
                ));

                $billRecord->save(false);
            } else if ($this->calcDemoMode($historyRecord)) {
                BillRecord::deleteAll(['iBillPk' => $bill['iBillPk']]);
            }
        }
    }

    /**
     * @param CreditCard $creditCard
     * @param array $paymentRecords
     * @return void
     */
    protected function calcCardBrandAllowed(CreditCard $creditCard, array $paymentRecords): void
    {
        $cardMap = [
            CreditCard::MASTERCARD => 'iMasterCard',
            CreditCard::VISA => 'iVisa',
            CreditCard::DISCOVER => 'iDiscover',
            CreditCard::AMERICAN_EXPRESS => 'iAmex'
        ];

        $cardBrandAllowed = false;
        if (array_key_exists($creditCard->cardBrand, $cardMap)) {
            $dbCardBrand = $cardMap[$creditCard->cardBrand];
            $cardBrandAllowed = !in_array(0, array_column($paymentRecords, $dbCardBrand), true);
        }

        if (!$cardBrandAllowed) {
            $creditCard->addError('cardBrand', sprintf('Card brand (%1$s) is not allowed', $creditCard->cardBrand));
        }
    }

    /**
     * @param string $cardOrCheck
     * @param PaymentTypeRecord $paymentTypeRecord
     * @return array{primaryForm: ProcessorCreditCard|ProcessorCheck, secondaryForm: ProcessorCreditCard|ProcessorCheck, merchantInfoData: array}
     */
    protected function createGatewayObjects(string $cardOrCheck, PaymentTypeRecord $paymentTypeRecord): array
    {
        $merchantInfoRecord = $paymentTypeRecord->merchantInfo;

        $merchantInfoData = [
            'iMerchantInfoPk' => $merchantInfoRecord->iMerchantInfoPk,
            'paymentTypeId' => $paymentTypeRecord->iPaymentTypePk,
            'gatewayEnvironment' => $merchantInfoRecord->gatewayEnvironment,
        ];

        if ($cardOrCheck === 'Check') {
            if (isset($merchantInfoRecord->checkProcessor)) {
                $checkFields = $merchantInfoRecord->checkProcessor->getProcessorFields()
                    ->joinWith(['fieldValues' => static function (BaseQuery $query) use ($merchantInfoRecord) {
                        $query->alias('fieldValues')
                            ->andWhere(['fieldValues.iMerchantInfoFk' => $merchantInfoRecord->iMerchantInfoPk]);
                    }])->all();

                if (!empty($checkFields)) {
                    [$primaryForm, $secondaryForm] = ProcessorCheck::createForms($checkFields);
                }
            }

            $merchantInfoData['processorPk'] = $merchantInfoRecord->iCheckPaymentProcessorFk;
        } else { // $cardOrCheck === 'CreditCard'
            if (isset($merchantInfoRecord->creditCardProcessor)) {
                $cardFields = $merchantInfoRecord->creditCardProcessor->getProcessorFields()
                    ->joinWith(['fieldValues' => static function (BaseQuery $query) use ($merchantInfoRecord) {
                        $query->alias('fieldValues')
                            ->andWhere(['fieldValues.iMerchantInfoFk' => $merchantInfoRecord->iMerchantInfoPk]);
                    }])->all();

                if (!empty($cardFields)) {
                    [$primaryForm, $secondaryForm] = ProcessorCreditCard::createForms($cardFields);
                }
            }

            $merchantInfoData['processorPk'] = $merchantInfoRecord->iCCPaymentProcessorFk;
        }

        return [$primaryForm ?? [], $secondaryForm ?? [], $merchantInfoData];
    }

    /**
     * @param array $params
     * @return Response
     * @throws InvalidConfigException
     * @throws YiiDbException
     */
    private function auth(array $params): Response
    {
        $response = new Response();

        if (empty($params['paymentMethod'])) {
            $response->errors['paymentMethod'] = 'Payment Method data is missing';
            return $response;
        }

        /** @var string $paymentMethod */
        $paymentMethod = $params['paymentMethod'];
        unset($params['paymentMethod']);

        if (!in_array($paymentMethod, [Pay::PAYMENT_METHOD_CARD, Pay::PAYMENT_METHOD_CHECK], true)) {
            $response->errors['paymentMethod'] = 'A valid option was not provided, valid options are "CreditCard" or "Check';
            return $response;
        }

        if (empty($params['customer'])) {
            $response->errors['customer'] = 'Customer data is missing';
            return $response;
        }

        $paymentTypeRecord = PaymentTypeRecord::findOne(['iPaymentTypePk' => array_unique(array_column($params['bills'], 'paymentTypePk'))]);

        if ($paymentTypeRecord === null) {
            $response->errors['bills']['paymentTypePk'] = 'Payment type not found';
            return $response;
        }

        $oCustomer = $this->createCustomer($params, $response);

        ### If there were errors related to lookupCityState
        if ($response->errors) {
            return $response;
        }

        $isCard = $paymentMethod === Pay::PAYMENT_METHOD_CARD;
        $oCreditCard = null;
        if ($isCard) {
            if (empty($params['creditCard'])) {
                $response->errors['creditCard'] = 'Credit card data is missing';
            } else {
                # check payment type to verify card payment is allowed.
                $isPaymentMethodAllowed = $this->isCardPaymentMethodAllowed([$paymentTypeRecord]);

                if (!$isPaymentMethodAllowed) {
                    $response->errors['paymentMethod'] = 'Payment method can only be "' . Pay::PAYMENT_METHOD_CHECK . '".';
                    return $response;
                }

                $oCreditCard = $this->createCreditCard($params);

                $this->calcCardBrandAllowed($oCreditCard, [$paymentTypeRecord]);

                if (!$oCreditCard->validate(null, false)) {
                    $response->errors['creditCard'] = $oCreditCard->errors;
                    return $response;
                }
            }
        }

        $isCheck = $paymentMethod === Pay::PAYMENT_METHOD_CHECK;
        $oBankAccount = null;
        if ($isCheck) {
            if (empty($params['bankAccount'])) {
                $response->errors['bankAccount'] = 'Bank account data is missing';
            } else {
                # check payment type to verify check payment is allowed.
                $isPaymentMethodAllowed = $this->isCheckPaymentMethodAllowed([$paymentTypeRecord]);

                if (!$isPaymentMethodAllowed) {
                    $response->errors['paymentMethod'] = 'Payment method can only be "' . Pay::PAYMENT_METHOD_CARD . '".';
                    return $response;
                }

                $oBankAccount = $this->createBankAccount($params);

                if (!$oBankAccount->validate()) {
                    $response->errors['bankAccount'] = $oBankAccount->errors;
                }
            }
        }

        ### If there were errors with $oCreditCard or $oBankAccount
        if ($response->errors) {
            return $response;
        }

        [$primaryForm, $secondaryForm, $merchantInfoData] = $this->createGatewayObjects($paymentMethod, $paymentTypeRecord);
        ### $secondaryForm, not used
        unset($secondaryForm);

        $gatewayCredentials = array_merge($merchantInfoData, ($primaryForm[$paymentMethod === 'Check' ? 'eCheck' : 'creditCard'] ?? []));

        $processorRecord = PaymentProcessorRecord::findOne(['iPaymentProcessorPk' => $gatewayCredentials['processorPk']]);

        if ($processorRecord === null) {
            $response->errors['paymentProcessor'] = 'Payment processor not found';
            return $response;
        }

        /** @var BaseGateway $gateway */
        $gateway = new $processorRecord->className($gatewayCredentials);

        ### Switch the gateway endpoint if the environment is not PROD
        if ((YII_ENV_DEV || YII_ENV_SANDBOX) && $gatewayCredentials['gatewayEnvironment'] === 'live') {
            $gateway->environment = BaseGateway::PRODUCTION;
        }

        $transaction = new Transaction();
        $transaction->customer = $oCustomer;
        $transaction->amount = 1.0;
        $transaction->gatewayCredentials = $gatewayCredentials;

        if ($isCard) {
            $transaction->paymentMethod = Pay::PAYMENT_METHOD_CARD;
            $transaction->creditCard = $oCreditCard;

            /** @var TransactionResponse $transactionResponse */
            $transactionResponse = $gateway->authorizeCreditCard($transaction);

            ### TODO: Maybe split this out to a separate call to do the VOID
            if ($transactionResponse->isError) {
                $response->errors = [$transactionResponse->responseCode . ': ' . $transactionResponse->responseMessage];
            } else {
                ### MagtekTSYS uses refTransId/refAuthCode, all other gateways use transactionId/authCode
                $transaction->transactionId = $transactionResponse->transactionId;
//                $transaction->refTransId = $transactionResponse->transactionId;
                $transaction->authCode = $transactionResponse->authCode;
//                $transaction->refAuthCode = $transactionResponse->authCode;

                $gateway->voidTransaction($transaction);
                $response->success = true;
                $response->object = 'list';
            }
        } else {
            $response = (new NachaService())->verify([
                'paymentTypePk' => $paymentTypeRecord->iPaymentTypePk,
                'routingNumber' => $oBankAccount->routingNumber,
                'accountNumber' => $oBankAccount->accountNumber,
            ]);
        }

        return $response;
    }

    /**
     * @param array $params
     * @return CreditCard
     */
    protected function createCreditCard(array &$params): CreditCard
    {
        $oCreditCard = new CreditCard();
        $oCreditCard->setAttributes($params['creditCard']);
        unset($params['creditCard']);

        ### Recalculate the cardBrand because it can be provided in an inconsistent manner
        if (!empty($oCreditCard->cardNumber)) {
            $oCreditCard->cardBrand = CardValidator::getCardBrand($oCreditCard->cardNumber);
        }

        return $oCreditCard;
    }

    /**
     * @param array $params
     * @return BankAccount
     */
    protected function createBankAccount(array &$params): BankAccount
    {
        $oBankAccount = new BankAccount();
        $oBankAccount->setAttributes($params['bankAccount']);
        unset($params['bankAccount']);

        return $oBankAccount;
    }

    /**
     * @param array $params
     * @param Response $response
     * @return Customer
     */
    protected function createCustomer(array &$params, Response $response): Customer
    {
        $aCustomer = $params['customer'];

        if (!empty($aCustomer['address'])) {
            $oAddress = new Address();
            $oAddress->setAttributes($aCustomer['address']);
            Helper::lookupCityState($oAddress, $response);
            $aCustomer['address'] = $oAddress;
        }

        $oPhone = new Phone();
        if (empty($aCustomer['phone'])) {
            $oPhone->phoneNumber = '(*************';
        } else {
            $oPhone->setAttributes($aCustomer['phone']);
        }
        $aCustomer['phone'] = $oPhone;

        $oCustomer = new Customer();
        $oCustomer->setAttributes($aCustomer);
        unset($params['customer']);

        return $oCustomer;
    }

    /**
     * @param HistoryRecord[] $historyRecords
     * @return array
     */
    private function calcMerchantEmails(array $historyRecords): array
    {
        $historyEmails = [];
        foreach ($historyRecords as $historyRecord) {
            $historyEmails[] = array_filter(
                array_map('trim', explode("\r\n", $historyRecord->paymentType->cMobileReceiptEmail))
            );
        }

        return array_unique(array_merge(...$historyEmails));
    }


    /**
     * Create a new visitor, address, payment profile and scheduled payment profile
     *
     * @param Pay $pay
     * @param array $bill
     * @return void
     * @throws BaseException
     * @throws NotFoundHttpException
     */
    public function autoAddScheduledPaymentForNewUser(Pay $pay, array $bill): void
    {
        $paymentType = PaymentTypeRecord::findOne(['iPaymentTypePk' => $bill['paymentTypePk']]);
        if ($paymentType === null) {
            throw new NotFoundHttpException('Payment type not found');
        }

        ### Create visitor
        $oVisitor = new VisitorProfileRecord();
        $oVisitor->cEmail = $pay->customer->emailAddress;
        $oVisitor->cPassword = Helper::encryptData(Yii::$app->security->generateRandomString(16));
        $oVisitor->cLastLoggedIn = $this->dateTime();
        $oVisitor->save();

        $this->visitorUser = $oVisitor;

        ### Create the new address and associate it to the payment profile to be created
        $addressRecord = $this->newAddressRecord($pay->customer);

        ### Create payment profile
        $paymentProfileRecord = $this->newPaymentProfileRecord($pay, $addressRecord);

        ### Create scheduled payment profile & associated locators
        $this->newScheduledPaymentProfileRecord($bill, $paymentType, $paymentProfileRecord);
    }

    /**
     * Create a scheduled payment profile for an existing visitor
     *
     * @param Pay $pay
     * @param array $bill
     * @return void
     * @throws NotFoundHttpException
     * @throws YiiDbException
     */
    public function autoAddScheduledPaymentForExistingUser(Pay $pay, array $bill): void
    {
        $paymentType = PaymentTypeRecord::findOne(['iPaymentTypePk' => $bill['paymentTypePk']]);
        if ($paymentType === null) {
            throw new NotFoundHttpException('Payment type not found');
        }

        if (empty($pay->paymentProfileId)) {
            ### Create the new address and associate it to the payment profile to be created
            $addressRecord = $this->newAddressRecord($pay->customer);

            ### Create payment profile
            $paymentProfileRecord = $this->newPaymentProfileRecord($pay, $addressRecord);
        } else {
            ### Use the current payment method to set up scheduled payment
            $paymentProfileRecord = PaymentProfileRecord::findOne(['id' => $pay->paymentProfileId]);
        }

        ### Create scheduled payment profile & associated locators
        $this->newScheduledPaymentProfileRecord($bill, $paymentType, $paymentProfileRecord);
    }


    /**
     * Create a new AddressRecord model with some pre-filled out values
     *
     * @param Customer $customer
     * @return AddressRecord
     * @throws YiiDbException
     */
    public function newAddressRecord(Customer $customer): AddressRecord
    {
        $addressRecord = new AddressRecord;
        $addressRecord->loadDefaultValues();
        $addressRecord->cName = $customer->fullName;
        $addressRecord->cAddress = $customer->address->addressLine1;
        $addressRecord->cCity = $customer->address->city;
        $addressRecord->cState = $customer->address->state;
        $addressRecord->cZip = $customer->address->postalCode;
        $addressRecord->cPhone = $customer->phone->phoneNumber;
        $addressRecord->save();
        $addressRecord->link('visitorProfile', $this->visitorUser);

        return $addressRecord;
    }

    /**
     * Create a new PaymentProfileRecord model with some pre-filled out values
     *
     * @param Pay $pay
     * @param AddressRecord $addressRecord
     * @return PaymentProfileRecord
     * @throws YiiDbException
     */
    public function newPaymentProfileRecord(Pay $pay, AddressRecord $addressRecord): PaymentProfileRecord
    {
        $paymentProfileRecord = new PaymentProfileRecord;
        $paymentProfileRecord->loadDefaultValues();
        $paymentProfileRecord->cName = $pay->customer->fullName;
        $paymentProfileRecord->cProfileName = 'Payment method created by Bill Pay';

        if ($pay->creditCard) {
            $paymentProfileRecord->cCardNumber = $pay->creditCard->cardNumber;
            $paymentProfileRecord->cExpMonth = $pay->creditCard->expirationMonth;
            $paymentProfileRecord->cExpYear = $pay->creditCard->expirationYear;
            $paymentProfileRecord->iPaymentType = 1;
        }

        if ($pay->bankAccount) {
            $paymentProfileRecord->cAccountNumber = $pay->bankAccount->accountNumber;
            $paymentProfileRecord->cRoutingNumber = $pay->bankAccount->routingNumber;
            $paymentProfileRecord->cAccountType = $pay->bankAccount->accountType;
            $paymentProfileRecord->bankAccountType = $pay->bankAccount->bankAccountType;
            $paymentProfileRecord->iPaymentType = 2;
        }

        $paymentProfileRecord->iMobileVisitorFk = $this->visitorUser->id;
        $paymentProfileRecord->iAddressFk = $addressRecord->iAddressPk;
        ### Skipping validation on this model for now, I think there is an issue with the validateCreditCardExpiration validator
        //$paymentProfileRecord->save();
        $paymentProfileRecord->save(false);

        return $paymentProfileRecord;
    }

    /**
     * Create a new ScheduledPaymentProfileRecord model with some pre-filled out values
     *
     * @param array $bill
     * @param PaymentTypeRecord $paymentType
     * @param PaymentProfileRecord $paymentProfileRecord
     * @return void
     * @throws YiiDbException
     */
    public function newScheduledPaymentProfileRecord(array $bill, PaymentTypeRecord $paymentType, PaymentProfileRecord $paymentProfileRecord): void
    {
        $oScheduledProfile = new ScheduledPaymentProfileRecord;
        $oScheduledProfile->loadDefaultValues();
        $oScheduledProfile->iMobileVisitorFk = $this->visitorUser->id;
        $oScheduledProfile->dStartDate = ScheduledPaymentProfileRecord::calculateScheduleStartDate($paymentType->cRecurringFrequency);
        $oScheduledProfile->iNumberofPayments = 9999; ### Until I cancel
        $oScheduledProfile->iPaymentDay = date('j') > 28 ? 28 : date('j'); ### Today day of the month 1-28
        $oScheduledProfile->iPaymentSchedule = $paymentType->cRecurringFrequency;
        $oScheduledProfile->iPaymentTypeFk = $paymentType->iPaymentTypePk;
        $oScheduledProfile->iMobilePaymentProfileFk = $paymentProfileRecord->id;

        if ($paymentType->isTypePosted()) {
            $oScheduledProfile->iPaymentMax = (float)$bill['billAmount'];
        }

        $bPaymentTypeIsInstantOrSpp = in_array($paymentType->Type, [$paymentType::TYPE_INSTANT, $paymentType::TYPE_SPP], true);
        $bPostedAutoAddToday = $paymentType->iRecurring === 4 && $paymentType->cRecurringFrequency === '5';

        if ($bPaymentTypeIsInstantOrSpp) {
            $oScheduledProfile->iPaymentAmount = (float)$bill['billAmount'];

            if ($bPostedAutoAddToday) {
                $oScheduledProfile->iPaymentAmount = 0;
            }
        }

        if ($bPostedAutoAddToday && $bPaymentTypeIsInstantOrSpp) {
            $oScheduledProfile->cProfileName = "Auto Add -- $paymentType->PaymentType, BTS: $paymentType->Type, Schedule: Today";
        } else {
            $oScheduledProfile->cProfileName = "Auto Add -- $paymentType->PaymentType";
        }

        if (empty($bill['uniqueBillIdentifier'])) {
//            Yii::info(['PayService->pay(), setting up "auto add", missing uniqueBillIdentifier' => [
//                '$bill' => $bill,
//            ]], __METHOD__);
            $bill['uniqueBillIdentifier'] = 'Missing UID';
        }

        ### To set up a scheduled payment for a posted bill the UID is required to associate future bills/payments
        $oScheduledProfile->cUniqueID = $bill['uniqueBillIdentifier'];

        $oScheduledProfile->save();

        # Create scheduled payment profile locators
        if (isset($bill['paymentTypeLocators'])) {
            foreach ($bill['paymentTypeLocators'] as $locator) {
                $locatorRecord = PaymentTypeLocatorRecord::findOne([
                    'iPaymentTypeFk' => $bill['paymentTypePk'],
                    'locatorNum' => $locator['locatorNum']
                ]);

                if ($locatorRecord) {
                    $oScheduledProfile->link('paymentTypeLocators', $locatorRecord, ['locatorValue' => $locator['locatorValue']]);
                }
            }
        }
    }

    /**
     * @param Pay $pay
     * @param array $bill
     * @return void
     * @throws BaseException
     * @throws NotFoundHttpException
     */
    public function processAutoAddScheduledPaymentProfile(Pay $pay, array $bill): void
    {
        ### We don't need organization or paymentType array
        unset($bill['organization'], $bill['paymentType']);

        ### Check to see if the visitor logged in or checked out as guest and used an email address that is already associated to a visitor profile
        if ($this->visitorUser === null) {
            $this->visitorUser = VisitorProfileRecord::findOne(['cEmail' => $pay->customer->emailAddress]);
        }

        if ($this->visitorUser) {
            $this->autoAddScheduledPaymentForExistingUser($pay, $bill);
        } else {
            $this->autoAddScheduledPaymentForNewUser($pay, $bill);
        }
    }

    /**
     * @param array $params
     * @return array
     */
    protected function splitParams(array $params): array
    {
        $customerParams = [];
        $bankAccountParams = [];
        $creditCardParams = [];

        if (!empty($params['customer'])) {
            $customerParams = $params['customer'];
            unset($params['customer']);
        }

        if (!empty($params['bankAccount'])) {
            $bankAccountParams = $params['bankAccount'];
            unset($params['bankAccount']);
        }

        if (!empty($params['creditCard'])) {
            $creditCardParams = $params['creditCard'];
            unset($params['creditCard']);
        }

        return [$params, $customerParams, $bankAccountParams, $creditCardParams];
    }

    /**
     * @param array $paymentTypeRecords
     * @return bool
     */
    public function isCardPaymentMethodAllowed(array $paymentTypeRecords): bool
    {
        return (bool)array_filter(
            array_column($paymentTypeRecords, 'paymentMethod'),
            static fn($item) => $item === 1 || $item === 3
        );
    }

    /**
     * @param array $paymentTypeRecords
     * @return bool
     */
    public function isCheckPaymentMethodAllowed(array $paymentTypeRecords): bool
    {
        return (bool)array_filter(
            array_column($paymentTypeRecords, 'paymentMethod'),
            static fn($item) => $item === 2 || $item === 3
        );
    }

    /**
     * @param Response $response
     * @param string|null $msg
     * @return Response
     */
    protected function fraudErrorResponse(Response $response, string $msg = null): Response
    {
        $response->errors[400] = $msg ?? 'Transaction failed. Please contact customer support. (EC7)';
        return $response;
    }
}
