<?php

namespace app\modules\v1\services;

use app\components\FtpService;
use app\models\BaseQuery;
use app\modules\v1\components\Helper;
use app\modules\v1\models\objects\Ftp;
use app\modules\v1\models\objects\Report;
use app\modules\v1\models\objects\ReportFormatWriter;
use app\modules\v1\models\objects\Response;
use app\modules\v1\models\records\PaymentTypeRecord;
use app\modules\v1\models\records\ReportRecord;
use DateMalformedStringException;
use DateTime;
use Exception;
use League\Flysystem\Ftp\FtpConnectionException;
use Yii;
use yii\base\InvalidConfigException;
use yii\helpers\Json;
use yii\rbac\Item;

class ReportService extends BaseService
{
    public static string $modelClass = ReportRecord::class;

    /**
     * @return string[]
     */
    public static function methods(): array
    {
        return ['POST', 'GET'];
    }

    /**
     * @param array $params
     * @return Response
     * @throws FtpConnectionException
     * @throws InvalidConfigException
     */
    public function run(array $params): Response {
        return match ($this->requestMethod) {
            'POST' => $this->generate($params),
            default => $this->index($params),
        };
    }

    /**
     * @param array $params
     * @return Response
     */
    private function index(array $params): Response
    {
        $response = new Response();
        $response->object = 'list';

        $reportModel = new self::$modelClass;
        $user = $this->user->identity;

        $params['expand'] = '';

        if ($organizationId = $this->user->identity->organizationId) {
            $params['expand'] .= 'organizations,authItems';

            $params['filter']['or'] = [
                ['organization.iOrganizationPk' => ['eq' => $organizationId]],
                [
                    'and' => [
                        ['authItem.name' => ['eq' => $user->assignedRole->itemName]],
                        ['authItem.type' => ['eq' => Item::TYPE_ROLE]]
                    ]
                ]
            ];
        }

        $reports = $reportModel->search($params, [['report.dateDeleted' => NULL]]);

        $response->success = empty($response->errors);

        $response->data = $reports;
        return $response;
    }

    /**
     * @param array $params
     * @return Response
     * @throws Exception
     * @throws FtpConnectionException
     * @throws InvalidConfigException
     */
    private function generate(array $params): Response
    {
        $response = new Response();

        // Validate report
        $report = new Report();
        $report->setAttributes($params);

        if (!$report->validate()) {
            $response->errors = array_merge($report->errors, $response->errors);
            return $response;
        }

        // Check if report exists
        $reportRecord = ReportRecord::find()
            ->joinWith(['templates' => static function (BaseQuery $query) use ($params) {
                $query->andWhere(['reportTemplate.guid' => $params['templateId']])
                    ->andWhere(['reportTemplate.dateDeleted' => NULL]);
            }, 'parameters' => static function (BaseQuery $query) {
                $query->andWhere(['reportParameter.dateDeleted' => NULL]);
            }])
            ->andWhere(['report.guid' => $params['reportId']])
            ->limit(1)
            ->asArray()
            ->one();

        // Load dynamic parameters and then validate
        foreach ($reportRecord['parameters'] as $parameter) {
            $report->defineAttribute($parameter['name']);
            $report->addRule([$parameter['name']], 'safe');

            # add requirements to report for required parameters
            # commented out so the last* text concept works for dates
            # and left here for future if needed
            # TODO: update this to allow require and text strings for date inputs
            if ($parameter['isRequired']) {
                if ($parameter['allowMultiple']) {
                    $report->addRule($parameter['name'], function ($attribute) use ($report, $parameter) {
                        if (!is_array($report->{$attribute}) || empty(array_filter($report->{$attribute}))) {
                            $report->addError($attribute, $parameter['label'] . ' requires at least one value.');
                        }
                    }, ['skipOnEmpty' => false]);
                } else {
                    $report->addRule($parameter['name'], 'required', ['message' => $parameter['label'] . ' cannot be blank.']);
                }
            }

            # check if iOrganizationPk or paymentTypeId are passed in
            if ($parameter['columnName'] === 'iOrganizationPk' &&
                ($assignedOrganizations = Yii::$app->user->identity->assignedOrganizations) &&
                $report->hasAttribute('organizationId'))
            {
                $report->addRule('organizationId', function ($attribute) use ($report, $assignedOrganizations) {
                    $assignedOrganizationIds = array_column($assignedOrganizations, 'iOrganizationPk');
                    $invalidOrganizationIds = array_diff((array) $report->{$attribute}, $assignedOrganizationIds);

                    if (!empty($invalidOrganizationIds)) {
                        $report->addError($attribute, sprintf('You do not have access to the following organization ids: [%s]', implode(', ', $invalidOrganizationIds)));
                    }
                }, ['skipOnEmpty' => false]);
            }

            if ($parameter['columnName'] === 'iPaymentTypePk' && $report->hasAttribute('paymentTypeId')) {
                $report->addRule('paymentTypeId', function($attribute) use ($report) {
                    $paymentTypes = PaymentTypeRecord::find()->select(['iPaymentTypePk'])
                        ->andWhere(['iOrganizationFk' => $report->organizationId])
                        ->asArray()->all();
                    $assignedPaymentTypeIds = array_column($paymentTypes, 'iPaymentTypePk');
                    $invalidPaymentTypeIds = array_diff((array) $report->{$attribute}, $assignedPaymentTypeIds);

                    if (!empty($invalidPaymentTypeIds)) {
                        $report->addError($attribute, sprintf('You do not have access to the following payment type ids: [%s]', implode(', ', $invalidPaymentTypeIds)));
                    }
                }, ['skipOnEmpty' => false]);
            }
        }

        $parameters = $params['parameters'];

        $report->setAttributes($parameters);
        $report->settings = array_change_key_case($report->settings, CASE_UPPER);

        if (($organizationId = $this->user->identity->organizationId) &&
            empty($report->organizationId) && $report->hasAttribute('organizationId')) {
            $report->organizationId = [$organizationId];
        }

        if ($report->validate()) {
            $reportResults = self::generateReport($reportRecord, $report);
            extract($reportResults);

            # get file format
            $formatWriter = new ReportFormatWriter($reportRecord['templates'][0]);
            $formatWriter->fileName = 'report_' . date('Ymd');
            $formatWriter->setData($results);
            $formatWriter->setParameters($parameters);

            $attach = $formatWriter->attach();
            $method = strtoupper($report->method);
            $totalResults = count($results);

            $sendMessages = [];
            if (in_array($method, [Report::METHOD_EMAIL, Report::METHOD_BOTH], true)) {
                $this->emailReport($response, $reportRecord, $report, $totalResults, $parameters, $attach);
                $sendMessages[] = 'emailed';
            }

            if (in_array($method, [Report::METHOD_FTP, Report::METHOD_BOTH], true)) {
                $this->ftpReport($response, $reportRecord, $report, $parameters, $attach);
                $sendMessages[] = 'uploaded to server';
            }

            if ($response->success) {
                $response->object = 'report';
                $response->data['report'] = sprintf('Report was successfully %s.', implode(' and ', $sendMessages));
            }
        } else {
            $response->errors = array_merge($report->errors, $response->errors);
            return $response;
        }

        return $response;
    }

    /**
     * @param Response $response
     * @param array $reportRecord
     * @param Report $report
     * @param int $totalResults
     * @param array $parameters
     * @param array $attach
     * @return void
     */
    private function emailReport(Response $response, array $reportRecord, Report $report,  int $totalResults, array $parameters, array $attach): void
    {
        $emailSettings = $report->settings[Report::METHOD_EMAIL];

        $mailer = Yii::$app->mailer
            ->compose('@v1Views/mail/report/email', [
                'reportRecord' => $reportRecord,
                'parameters' => $parameters,
                'emailMsg' => $emailSettings['message'] ?? '',
                'totalResults' => $totalResults
            ]);
        $mailer->setFrom(Yii::$app->params['supportEmail']);
        $mailer->setSubject('Xpress-pay Report - ' . $emailSettings['subject']);
        $mailer->setTo($emailSettings['emailTo']);

        if (!empty($emailSettings['emailCc'])) { $mailer->setCc($emailSettings['emailCc']); }
        if (!empty($emailSettings['emailBcc'])) { $mailer->setBcc($emailSettings['emailBcc']); }

        $fileName = empty($emailSettings['appendDate']) ? $emailSettings['filename'] : $this->appendDateToFilename($emailSettings);

        if ($attach['extension'] === 'xlsx') {
            $mailer->attach($attach['stream'], ['fileName' => $fileName, 'contentType' => $attach['contentType']]);
        } else {
            $mailer->attachContent($attach['stream'], ['fileName' => $fileName, 'contentType' => $attach['contentType']]);
        }

        if ($mailer->send()) {
            $response->success = true;
        }
    }

    /**
     * @param Response $response
     * @param array $reportRecord
     * @param Report $report
     * @param array $parameters
     * @param array $attach
     * @return void
     * @throws FtpConnectionException
     */
    private function ftpReport(Response $response, array $reportRecord, Report $report, array $parameters, array $attach): void
    {
        $ftpSettings = $report->settings[Report::METHOD_FTP];

        $ftp = new Ftp();
        $ftp->setAttributes($ftpSettings);

        if ($ftp->validate()) {
            if ($attach['extension'] === 'xlsx') {
                $fp = fopen($attach['stream'], 'rb+');
            } else {
                $fp = fopen('php://temp', 'rb+');
                fwrite($fp, $attach['stream']);
                rewind($fp);
            }

            $fileName = empty($ftpSettings['appendDate']) ? $ftpSettings['filename'] : $this->appendDateToFilename($ftpSettings);

            $ftpService = new FtpService($ftp->attributes);
            $ftpService->connect();
            $ftpService->addAdditionalErrorParameters([
                '$reportRecord' => $reportRecord,
                '$report->attributes' => $report->attributes,
                '$parameters' => $parameters
            ]);

            $fileName = (empty($ftpSettings['dir']) ? '' : $ftpSettings['dir'] . DIRECTORY_SEPARATOR) . $fileName;
            $ftpResponse = $ftpService->upload($fileName, $fp);

            if ($ftpResponse['success']) {
                $response->success = true;
            } else {
                $response->errors[] = $ftpResponse['error'];
            }

            if (is_resource($fp)) {
                fclose($fp);
            }
        } else {
            $response->errors = $ftp->errors;
        }
    }

    /**
     * @param array $reportRecord
     * @param Report $report
     * @return array
     * @throws Exception
     */
    private static function generateReport(array $reportRecord, Report $report): array
    {
        $reportTemplate = $reportRecord['templates'][0];
        $report->htmlContent = $reportTemplate['twigContent'];

        # Build WHERE parameters
        $whereClause = [];
        $parameters = [];

        foreach ($reportRecord['parameters'] as $parameter) {
            $tableName = empty($parameter['tableNameAlias']) ? $parameter['tableName'] : $parameter['tableNameAlias'];
            $columnName = $parameter['columnName'];
            $paramColumn = $tableName . '.' . $columnName;

            if (is_array($report->{$parameter['name']})) {
                $paramPostedValue = $report->{$parameter['name']};
                array_walk($paramPostedValue, static function(& $value) {
                    $value = html_entity_decode($value);
                });
            } else {
                $paramPostedValue = html_entity_decode($report->{$parameter['name']});
            }

            # check if json format
            if (Helper::isJson($paramPostedValue)) {
                $paramPostedValue = Json::decode($paramPostedValue);
            }

            # ignore empty values
            if ((!is_int($paramPostedValue) && empty($paramPostedValue)) || $paramPostedValue === false) {
                continue;
            }

            if ($parameter['isNotSearchable']) {
                $parameters[$parameter['name']] = $paramPostedValue;
                continue;
            }

            if (in_array($parameter['fieldType'], ['dateInput', 'dateTimeInput'])) {
                if ($parameter['fieldType'] === 'dateTimeInput') {
                    $paramColumn = 'TRY_CAST(' . $paramColumn . ' as DATETIME)';
                    $paramFormat = 'Y-m-d H:i:s';
                } else {
                    $paramColumn = 'TRY_CAST(' . $paramColumn . ' as DATE)';
                    $paramFormat = 'Y-m-d';
                }

                try {
                    $paramPostedValue = (new DateTime($paramPostedValue))->format($paramFormat);
                } catch (DateMalformedStringException $e) {
                    // Log the error and skip this parameter
                    \Yii::error("Invalid date format in report parameter: '{$paramPostedValue}' - " . $e->getMessage(), __METHOD__);
                    continue; // Skip this parameter if date is invalid
                }
            }

            switch ($parameter['compareType']) {
                case 'like':
                    $whereClause[] = sprintf('(%1$s like \'\%%2$s\%\')', $paramColumn, $paramPostedValue);
                    break;
                case 'notLike':
                    $whereClause[] = sprintf('(%1$s not like \'\%%2$s\%\')', $paramColumn, $paramPostedValue);
                    break;
                case 'startsWith':
                    $whereClause[] = sprintf('(%1$s like \'%2$s\%\')', $paramColumn, $paramPostedValue);
                    break;
                case 'endsWith':
                    $whereClause[] = sprintf('(%1$s like \'\%%2$s\')', $paramColumn, $paramPostedValue);
                    break;
                default:
                    if (is_array($paramPostedValue)) {
                        $compareType = $parameter['compareType'] === '!=' ? 'not in' : 'in';
                        $whereClause[] = sprintf('(%1$s %2$s (\'%3$s\'))', $paramColumn, $compareType, implode('\',\'', $paramPostedValue));
                    } else {
                        $whereClause[] = sprintf('(%1$s %2$s \'%3$s\')', $paramColumn, $parameter['compareType'], $paramPostedValue);
                    }
                    break;
            }

            $parameters[$parameter['name']] = $paramPostedValue;
        }

        # switch out WHERE clause
        if (empty($whereClause)) {
            $queryBuilder = str_replace('[WHERE]', '1=1', $reportRecord['queryBuilder']);
        } else {
            $queryBuilder = str_replace('[WHERE]', implode(' AND ', $whereClause), $reportRecord['queryBuilder']);
        }

        $queryBuilder = Json::decode($queryBuilder);
        $results = Yii::$app->db->createCommand($queryBuilder)->queryAll();

        return ['parameters' => $parameters, 'results' => $results];
    }

    /**
     * @param array $settings
     * @return string
     */
    private function appendDateToFilename(array $settings): string
    {
        $fileName = $settings['filename'];
        $extension_pos = strrpos($fileName, '.');
        $date = date($settings['appendDate']);

        if ($extension_pos === false) {
            $fileName .= $date;
        } else {
            $fileName = substr($fileName, 0, $extension_pos) . $date . substr($fileName, $extension_pos);
        }

        return $fileName;
    }
}