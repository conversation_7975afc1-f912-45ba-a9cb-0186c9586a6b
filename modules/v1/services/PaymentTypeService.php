<?php

namespace app\modules\v1\services;

use app\modules\v1\models\objects\Response;
use app\modules\v1\models\records\PaymentTypeRecord;
use DateMalformedStringException;
use DateTime;
use yii\base\InvalidConfigException;
use yii\helpers\ArrayHelper;

class PaymentTypeService extends BaseService
{
    public static string $modelClass = PaymentTypeRecord::class;

    /**
     * @return string[]
     */
    public static function methods(): array
    {
        return ['GET'];
    }

    /**
     * @param array $params
     * @return Response
     * @throws DateMalformedStringException
     */
    public function run(array $params): Response
    {
        switch ($this->requestMethod) {
            default: # GET
                $response = $this->index($params);
                break;
        }

        return $response;
    }

    /**
     *
     * @param array $params
     * @return Response
     * @throws DateMalformedStringException
     * @throws InvalidConfigException
     */
    private function index(array $params): Response
    {
        $response = new Response;

        if (!empty($this->id)) {
            $paymentTypePk = (int)$this->id;

            // Attempt to clear disabledDateTime if expired
            self::$modelClass::clearDisabledIfExpired($paymentTypePk, PayService::PAYMENT_TYPE_REENABLE_AFTER_SECONDS);
        }

        /** @var PaymentTypeRecord $paymentTypeModel */
        $paymentTypeModel = new self::$modelClass();
        $params['filter']['paymentType.Active'] = 1;
        ### Exclude "Outgoing payment"
        $params['filter']['activityType']['neq'] = 3;

        if ($this->id || isset($params['filter']['guid'])) {
            if ($this->id) {
                $params['filter']['iPaymentTypePk'] = $this->id;
            } else {
                $params['filter']['paymentType.guid'] = $params['filter']['guid'];
                unset($params['filter']['guid']);
            }

            if (isset($params['filter']['paymentType.Active'])) {
                unset($params['filter']['paymentType.Active']);
            }

            $paymentTypes = $paymentTypeModel->search($params);

            if (empty($paymentTypes)) {
                $response->errors['id'] = 'Payment Type not found';
            } else {
                $paymentType = $paymentTypes[0];

                if (isset($paymentType['paymentTypeLocators'])) {
                    self::sortPaymentTypeLocators($paymentType);
                }

                $response->success = true;
                $response->object = 'paymentType';
                $response->data = $paymentType;

                $oDateTimeNow = new DateTime();
                ### $paymentType['cActiveAfter'] && $paymentType['cActiveBefore'] are not always present
                $oDateTimeStart = isset($paymentType['cActiveAfter']) ? new DateTime($paymentType['cActiveAfter']) : new DateTime();
                $oDateTimeEnd = isset($paymentType['cActiveBefore']) ? new DateTime($paymentType['cActiveBefore']) : new DateTime();
                $closedPaymentPeriod = 'Merchant has ended the payment period';
                $waitingPaymentPeriod = sprintf('Payments of this type are not being accepted until %s at %s',
                    $oDateTimeStart->format('m/d/Y'),
                    $oDateTimeStart->format('h:i a'),
                );

                if (!empty($paymentType['cActiveAfter']) && empty($paymentType['cActiveBefore'])) {
                    if ($oDateTimeStart > $oDateTimeNow) {
                        $response = new Response;
                        $response->data = $paymentType;
                        $response->message = $waitingPaymentPeriod;
                    }
                } elseif (empty($paymentType['cActiveAfter']) && !empty($paymentType['cActiveBefore'])) {
                    if ($oDateTimeEnd < $oDateTimeNow) {
                        $response = new Response;
                        $response->data = $paymentType;
                        $response->message = $closedPaymentPeriod;
                    }
                } elseif (!empty($paymentType['cActiveAfter']) && !empty($paymentType['cActiveBefore'])) {
                    if ($oDateTimeStart > $oDateTimeNow) {
                        $response = new Response;
                        $response->data = $paymentType;
                        $response->message = $waitingPaymentPeriod;
                    } else if ($oDateTimeEnd < $oDateTimeNow) {
                        $response = new Response;
                        $response->data = $paymentType;
                        $response->message = $closedPaymentPeriod;
                    }
                } elseif ($paymentType['Active'] !== 1 || $paymentType['organization']['Active'] !== 1 || !empty($paymentType['disabledDateTime'])) {
                    $response = new Response;
                    $response->data = $paymentType;
                    $response->message = 'This merchant is no longer accepting ePayments of this type. Please contact them directly for assistance.';
                }
            }
        } else {
            ### Date filter/restriction code
            $dateTime = (new DateTime())->format('Y-m-d H:i:s');
            $params['filter']['AND'] = [
                [
                    'OR' => [
                        ['paymentType.cActiveAfter' => null],
                        ['paymentType.cActiveAfter' => ''],
                        ['paymentType.cActiveAfter' => ['lte' => $dateTime]],
                    ]
                ],
                [
                    'OR' => [
                        ['paymentType.cActiveBefore' => null],
                        ['paymentType.cActiveBefore' => ''],
                        ['paymentType.cActiveBefore' => ['gte' => $dateTime]],
                    ]
                ],
            ];

            $paymentTypes = $paymentTypeModel->search($params);

            # Append object type to each object
            $paymentTypes = ArrayHelper::toArray($paymentTypes);
            array_walk($paymentTypes, static function (&$item) {
                $item = array_merge($item, ['object' => 'paymentType']);

                if (isset($item['paymentTypeLocators'])) {
                    self::sortPaymentTypeLocators($item);
                }
            });

            $response->success = true;
            $response->object = 'list';
            $response->data = $paymentTypes;
        }

        return $response;
    }

    /**
     * @param array $paymentType
     * @return void
     */
    private static function sortPaymentTypeLocators(array &$paymentType): void
    {
        usort($paymentType['paymentTypeLocators'], static fn($a, $b) => $a['locatorNum'] <=> $b['locatorNum']);
    }
}