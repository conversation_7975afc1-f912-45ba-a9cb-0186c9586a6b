<?php

namespace app\components;

use Yii;
use yii\log\Logger;
use app\models\objects\ErrorAlert;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;
use yii\web\UnauthorizedHttpException;
use Twi<PERSON>\Exceptions\TwilioException;
use GuzzleHttp\Exception\GuzzleException;

class ErrorAlertLogger extends Logger
{
    private const string SYSTEM = 'api';

    // #alerts-api slack channel
    private const string SLACK_WEBHOOK_URL = '*******************************************************************************';

    /**
     * @return string
     */
    public static function getSystem(): string
    {
        return self::SYSTEM;
    }

    /**
     * @return string
     */
    public static function getSlackWebhookUrl(): string
    {
        return self::SLACK_WEBHOOK_URL;
    }

    /**
     * @param array|string $message
     * @param int $level
     * @param string $category
     * @return void
     * @throws GuzzleException
     * @throws TwilioException
     *
     * Override the log() method to intercept all logs
     */
    public function log($message, $level, $category = 'application'): void
    {
        $ignoreClasses = [
            BadRequestHttpException::class,
            NotFoundHttpException::class,
            UnauthorizedHttpException::class,
        ];

        if (!YII_ENV_DEV && $level === self::LEVEL_ERROR && !self::isInstanceOfAny($message, $ignoreClasses)) {
            $errorAlert = new ErrorAlert();
            $errorAlert->setAttributes([
                'system' => self::SYSTEM,
                'ipAddress' => Yii::$app->request->userIP ?? gethostbyname(gethostname()),
                'message' => $message,
                'slackWebhookUrl' => self::SLACK_WEBHOOK_URL
            ]);
            $errorAlert->sendToPhone();
            $errorAlert->sendToSlack();
        }

        parent::log($message, $level, $category);
    }

    /**
     * @param $exception
     * @param array $classes
     * @return bool
     */
    private static function isInstanceOfAny($exception, array $classes): bool
    {
        foreach ($classes as $class) {
            if ($exception instanceof $class) {
                return true;
            }
        }
        return false;
    }
}