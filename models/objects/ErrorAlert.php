<?php

namespace app\models\objects;

use app\components\Twilio;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Throwable;
use Twilio\Exceptions\TwilioException;
use yii\base\Model;

/**
 * Class ErrorAlert
 * @package app\models\objects
 */
class ErrorAlert extends Model
{
    private const array PHONE_NUMBERS = [
        '2394625041',     # Ben
//        '4074734738',     # Craig
//        '3866790210',     # Matt
//        '6073453336',     # Peter
//        '6077451545',   # Jim
//        '6075910000',   # Ariana
    ];

    private const int MAX_MESSAGE_LENGTH = 30000;

    public ?string $title = null;
    public ?string $system = null;
    public ?string $ipAddress = null;
    public mixed $message = null;
    public ?string $slackWebhookUrl = null;

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            [['system', 'message', 'slackWebhookUrl'], 'required'],
            [['title', 'ipAddress'], 'safe'],
        ];
    }

    /**
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'title' => 'Title',
            'system' => 'System Name',
            'ipAddress' => 'IP Address',
            'message' => 'Error Message',
            'slackWebhookUrl' => 'Slack Webhook URL',
        ];
    }

    /**
     * @return bool
     */
    public function beforeValidate(): bool
    {
        if (!empty($this->message) && is_string($this->message)) {
            $this->message = unserialize($this->message, ['allowed_classes' => true]);
        }

        return parent::beforeValidate();
    }

    /**
     * @return void
     * @throws TwilioException
     */
    public function sendToPhone(): void
    {
        $message = $this->formatMessageText($this->ipAddress, $this->message);

        $twilio = new Twilio();
        foreach (self::PHONE_NUMBERS as $phoneNumber) {
            $twilio->sendText($phoneNumber, $message);
        }
    }

    /**
     * @return bool
     * @throws GuzzleException
     */
    public function sendToSlack(): bool
    {
        $message = $this->formatErrorSlack($this->ipAddress, $this->message);
        $message = $this->formatMessageSlack($message);

        $client = new Client();

        $response = $client->post($this->slackWebhookUrl, [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'text' => $message,
            ]
        ]);

        return $response->getStatusCode() === 200;
    }

    /**
     * @param string $ipAddress
     * @param mixed $message
     * @return string
     */
    private function formatMessageText(string $ipAddress, mixed $message): string
    {
        $title = $this->title ?? 'Error';

        if ($message instanceof \Throwable) {
            $message = sprintf(
                "System: %s\nEnvironment: %s\n\nipAddress: %s\n\nException: %s\n\nMessage: %s\n\nFile: %s:%s",
                $this->system,
                YII_ENV,
                $ipAddress,
                get_class($message),
                $message->getMessage(),
                $message->getFile(),
                $message->getLine(),
            );
        } elseif (is_array($message) || is_object($message)) {
            $message = "*" . $title . ":* ```" . print_r($message, true) . "```";
        } else {
            $message = "*" . $title . ":* " . $message;
        }

        $message = "*Environment:* " . YII_ENV . "\n" . preg_replace('/\s+/', ' ', $message);

        // Truncate to SMS length limit (160–320 chars max is typical)
        return mb_substr($message, 0, 320);
    }

    /**
     * @param string $ipAddress
     * @param mixed $message
     * @return string
     */
    private function formatErrorSlack(string $ipAddress, mixed $message): string
    {
        $title = $this->title ?? 'Error';

        if ($message instanceof Throwable) {
            $message = sprintf(
                "*ipAddress:* %s\n*Exception:* %s\n*Message:* %s\n*File:* %s:%s\n*Code:* %s\n*Stack:*\n```%s```",
                $ipAddress,
                get_class($message),
                $message->getMessage(),
                $message->getFile(),
                $message->getLine(),
                $message->getCode(),
                $message->getTraceAsString()
            );
        } elseif (is_array($message) || is_object($message)) {
            $message = "*" . $title . ":* \n```" . print_r($message, true) . "```";
        } else {
            $message = "*" . $title . ":* \n```" . $message . "```";
        }

        return "*Environment:* " . YII_ENV . "\n$message";
    }

    /**
     * @param string|array $message
     * @return string
     *
     * Sanitize and format the error payload for Slack
     */
    private function formatMessageSlack(string|array $message): string
    {
        // Convert arrays to readable strings
        if (is_array($message)) {
            $message = print_r($message, true);
        }

        // Remove unwanted control characters
        $message = preg_replace('/[^\PC\s]/u', '', $message);

        // Truncate if too long (Slack max payload is ~4KB total)
        if (strlen($message) > self::MAX_MESSAGE_LENGTH) {
            $message = substr($message, 0, self::MAX_MESSAGE_LENGTH) . "\n...(truncated)";
        }

        return $message;
    }
}